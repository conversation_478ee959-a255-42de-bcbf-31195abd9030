'use strict';

const { models, sequelize } = require('../models');
const { MissionDTO, PlayerMissionDTO, MissionRewardDTO } = require('../domain/entity/mission');
const logger = require('pomelo-logger').getLogger('mission-service', __filename);
const { Op } = require('sequelize');
const transactionUtils = require('../util/transactionUtils');
class MissionService {
  constructor(app) {
    this.app = app;
  }
  /**
   * Get all active missions for a player
   * @param {number} playerId - The player's ID
   * @param {Object} options - Additional options for filtering missions
   * @returns {Promise<Array>} - Array of missions with player progress
   */
  async getMissions(playerId, options = {}) {
    try {
      // Get current date for time-based filtering
      const currentDate = new Date();
      
      // Build query conditions
      const whereConditions = {
        is_active: 1,
        [Op.or]: [
          { start_time: null },
          { start_time: { [Op.lte]: currentDate } }
        ],
        [Op.or]: [
          { end_time: null },
          { end_time: { [Op.gte]: currentDate } }
        ]
      };
      
      // Add type filter if provided
      if (options.type) {
        whereConditions.type = options.type;
      }
      
      // Get all active missions
      const missions = await models.Missions.findAll({
        where: whereConditions,
        order: [['id', 'ASC']]
      });
      
      logger.info('[missionService.getMissions] Fetched missions:', missions);

      // Get player's progress for these missions
      const playerMissions = await models.PlayerMissions.findAll({
        where: {
          player_id: playerId,
          mission_id: missions.map(m => m.id)
        }
      });
      
      // Create a map for quick lookup
      const playerMissionMap = {};
      playerMissions.forEach(pm => {
        playerMissionMap[pm.mission_id] = pm;
      });
      
      // Combine mission data with player progress
      const result = missions.map(mission => {
        const playerMission = playerMissionMap[mission.id] || null;
        return new MissionDTO(mission, playerMission);
      });
      logger.info('[missionService.getMissions] result:', result);
      return result;
    } catch (error) {
      logger.error('[missionService.getMissions] Error in getMissions:', error);
      throw error;
    }
  }
  
  /**
   * Process mission progress for a player
   * This is a placeholder function that would be called after relevant game events
   * @param {number} playerId - The player's ID
   * @param {string} eventType - The type of event that occurred
   * @param {Object} eventData - Data related to the event
   * @returns {Promise<Object>} - Updated mission progress
   */
  async processMissionProgress(playerId, eventType, eventData) {
    try {
      // This is a placeholder implementation
      // In a real implementation, you would:
      // 1. Find missions that match the event type
      // 2. Check if the player has these missions
      // 3. Update progress based on event data
      // 4. Check if any missions are completed
      
      logger.info(`Processing mission progress for player ${playerId}, event: ${eventType}`);

      // lấy danh sách nhiệm vụ với type = '${eventType}'
      const missions = await models.Missions.findAll({
        where: {
          type: eventType, // 'WIN_HANDS',
          is_active: 1
        }
      });
      logger.info('[missionService.processMissionProgress] missions of type ${eventType}:', missions);
      
      // Mock implementation - just log the event
      logger.info('Event data:', JSON.stringify(eventData));
      switch (eventType) {
        case 'WIN_HANDS':
          // Update progress for missions related to playing games
          // lần lượt lặp qua từng nhiệm vụ và kiểm tra xem có dữ liệu của playerId tương ứng chưa, có rồi tình toán và cập nhật dữ liệu mới, chưa có thì thêm mới
          missions.forEach(async mission => {
            // const conditionJson = JSON.parse(mission.condition_json);
            const conditionJson = mission.condition_json;
            const missionWinCount = conditionJson.win_count || 0;

              // Tìm kiếm player mission có player_id và mission_id tương ứng
            let playerMission = await models.PlayerMissions.findOne({
              where: {
                player_id: playerId,
                mission_id: mission.id
              }
            });
            if (playerMission) {
              // kiểm tra trạng is_completed nếu bằng 0 thì tiếp tực thực hiện, nếu bằng 1 là hoàn thành rồi, thì dừng lại
              if (parseInt(playerMission.is_completed) === 1) {
                logger.info(`[missionService.processMissionProgress][WIN_HANDS] Mission ${mission.id} is already completed for player ${playerId}`);
                return;
              }

              // const progressJson = JSON.parse(playerMission.progress_json);
              const progressJson = playerMission.progress_json;
              const newWinCountOfPlayer = progressJson.win_count + 1;
              // So sánh newWinCountOfPlayer với missionWinCount, nếu bằng thì cập nhật player mission và cập nhật progress_json, is_compoleted = 1, completed_at = current date, nếu kkhông bằng thì cập nhật progress_json
              // Cập nhật progress_json, giá trị của progress_json là 1 mảng như sau: {"win_count": 3}
              if (newWinCountOfPlayer === missionWinCount) {
                await playerMission.update({
                  is_completed: 1,
                  completed_at: new Date(),
                  progress_json: { win_count: newWinCountOfPlayer },
                  completion_percent: 100
                });
              } else {
                await playerMission.update({
                  progress_json: { win_count: newWinCountOfPlayer },
                  completion_percent: Math.round((newWinCountOfPlayer / missionWinCount) * 100)
                });
              }
            } else {
              // Tạo mới player mission
              playerMission = await models.PlayerMissions.create({
                player_id: playerId,
                mission_id: mission.id,
                progress_json: { win_count: 1 },
                // completion_percent tính toán từ progress_json với giá trị win_count so với win_count của nhiệm vụ để tính ra số %
                completion_percent: Math.round((1 / missionWinCount) * 100),
                is_completed: 0,
                completed_at: null,
                reward_claimed: 0,
                claimed_at: null,
                created_at: new Date(),
              });
            }
          });

          break;

        case 'WIN_STREAK': // số ván thắng liên tiếp
          missions.forEach(async mission => {
            // const conditionJson = JSON.parse(mission.condition_json);
            const conditionJson = mission.condition_json;
            const missionWinStreak = conditionJson.win_streak || 0;
            // lấy danh sách transactions theo type = 'game' and player_id = playerId, sắp xếp id desc, với limit = số lần thắng liên tiếp trong điều kiện của nhiệm vụ
            const transactions = await models.Transactions.findAll({
              where: {
                type: 'game',
                player_id: playerId
              },
              order: [['id', 'DESC']],
              limit: missionWinStreak
            });

            logger.info('[missionService.processMissionProgress] transactions:', transactions);

            const winCount = transactionUtils.countItemsByCondition(transactions, 'action', 'win');
            logger.info(`[missionService.processMissionProgress][WIN_STREAK] Số giao dịch thắng: ${winCount}`);

            // check xem player đã có mission_id tương ứng chưa, nếu có thì cập nhật progress_json, nếu không thì tạo mới
            let playerMission = await models.PlayerMissions.findOne({
              where: {
                player_id: playerId,
                mission_id: mission.id
              }
            });

            if (!playerMission) {
              playerMission = await models.PlayerMissions.create({
                player_id: playerId,
                mission_id: mission.id,
                progress_json: { win_streak: winCount },
                // completion_percent tính toán từ progress_json với giá trị win_streak so với win_streak của nhiệm vụ để tính ra số %
                completion_percent: Math.round((winCount / missionWinStreak) * 100),
                is_completed: 0,
                completed_at: null,
                reward_claimed: 0,
                claimed_at: null,
                created_at: new Date(),
              });
            }
            logger.info(`[missionService.processMissionProgress][WIN_STREAK] playerMission: ${JSON.stringify(playerMission)}`);
            // kiểm tra trạng is_completed nếu bằng 0 thì tiếp tực thực hiện, nếu bằng 1 là hoàn thành rồi, thì dừng lại
            if (parseInt(playerMission.is_completed) === 1) {
              logger.info(`[missionService.processMissionProgress][WIN_STREAK] Mission ${mission.id} is already completed for player ${playerId}`);
              return;
            }

            // so sánh giá trị winCount với missionWinStreak, nếu bằng thì cập nhật player mission và cập nhật progress_json, is_compoleted = 1, completed_at = current date, nếu kkhông bằng thì cập nhật progress_json
            // Cập nhật progress_json, giá trị của progress_json là 1 mảng như sau: {"win_streak": 3}
            if (winCount === missionWinStreak) {
              await models.PlayerMissions.update({
                is_completed: 1,
                completed_at: new Date(),
                progress_json: { win_streak: winCount },
                completion_percent: 100
              }, {
                where: {
                  player_id: playerId,
                  mission_id: mission.id
                }
              });
            } else {
              await models.PlayerMissions.update({
                progress_json: { win_streak: winCount },
                completion_percent: Math.round((winCount / missionWinStreak) * 100)
              }, {
                where: {
                  player_id: playerId,
                  mission_id: mission.id
                }
              });
            }

          }); // end forEach missions

          break;
        
        case 'DAILY_LOGIN':
          missions.forEach(async mission => {
            const conditionJson = mission.condition_json;
            const missionDailyLogin = conditionJson.login_days || 0;
            const streakInputOfPlayer = eventData.streak || 1
            logger.info(`[missionService.processMissionProgress][DAILY_LOGIN] streakInputOfPlayer: ${streakInputOfPlayer}, missionDailyLogin: ${missionDailyLogin}, conditionJson: ${JSON.stringify(conditionJson)}`);

            let playerMission = await models.PlayerMissions.findOne({
              where: {
                player_id: playerId,
                mission_id: mission.id
              }
            });

            if (!playerMission) {
              const dataCreate = {
                player_id: playerId,
                mission_id: mission.id,
                progress_json: { login_days: 1 },
                completion_percent: Math.round((streakInputOfPlayer / missionDailyLogin) * 100),
                is_completed: 0,
                completed_at: null,
                reward_claimed: 0,
                claimed_at: null,
                created_at: new Date(),
              }
              logger.info(`[missionService.processMissionProgress][DAILY_LOGIN] dataCreate: ${JSON.stringify(dataCreate)}`);
              playerMission = await models.PlayerMissions.create(dataCreate);
            }
            logger.info(`[missionService.processMissionProgress][DAILY_LOGIN] playerMission: ${JSON.stringify(playerMission)}`);
            // kiểm tra trạng is_completed nếu bằng 0 thì tiếp tực thực hiện, nếu bằng 1 là hoàn thành rồi, thì dừng lại
            if (parseInt(playerMission.is_completed) === 1) {
              logger.info(`[missionService.processMissionProgress][DAILY_LOGIN] Mission ${mission.id} is already completed for player ${playerId}`);
              return;
            }

            // so sánh giá trị streakInputOfPlayer với missionDailyLogin, nếu bằng thì cập nhật player mission và cập nhật progress_json, is_compoleted = 1, completed_at = current date, nếu kkhông bằng thì cập nhật progress_json
            // Cập nhật progress_json, giá trị của progress_json là 1 mảng như sau: {"login_days": 7}
            if (streakInputOfPlayer === missionDailyLogin) {
              await models.PlayerMissions.update({
                is_completed: 1,
                completed_at: new Date(),
                progress_json: { login_days: streakInputOfPlayer },
                completion_percent: 100
              }, {
                where: {
                  player_id: playerId,
                  mission_id: mission.id
                }
              });
            } else {
              await models.PlayerMissions.update({
                progress_json: { login_days: streakInputOfPlayer },
                completion_percent: Math.round((streakInputOfPlayer / missionDailyLogin) * 100)
              }, {
                where: {
                  player_id: playerId,
                  mission_id: mission.id
                }
              });
            }

          });  

          break;
        
        case 'BUY_ITEM':
          missions.forEach(async mission => {
            const conditionJson = mission.condition_json;
            const itemCondition = conditionJson.item || 0;
            let playerMission = await models.PlayerMissions.findOne({
              where: {
                player_id: playerId,
                mission_id: mission.id
              }
            });

            if (!playerMission) {
              playerMission = await models.PlayerMissions.create({
                player_id: playerId,
                mission_id: mission.id,
                progress_json: { item: 1 },
                completion_percent: Math.round((1 / itemCondition) * 100),
                is_completed: 0,
                completed_at: null,
                reward_claimed: 0,
                claimed_at: null,
                created_at: new Date(),
              });
            }

            // kiểm tra trạng is_completed nếu bằng 0 thì tiếp tực thực hiện, nếu bằng 1 là hoàn thành rồi, thì dừng lại
            if (parseInt(playerMission.is_completed) === 1) {
              logger.info(`[missionService.processMissionProgress][BUY_ITEM] Mission ${mission.id} is already completed for player ${playerId}`);
              return;
            }

            // lấy số lượng item mà player đã mua theo playerId trong table: shop_purchase_logs
            const totalItems = await models.ShopPurchaseLogs.count({
              where: {
                player_id: playerId,
                // shop_item_id: itemCondition
              }
            });

            // const itemQuantity = playerItem.length || 0;

            // so sánh giá trị itemCondition với 1, nếu bằng thì cập nhật player mission và cập nhật progress_json, is_compoleted = 1, completed_at = current date, nếu kkhông bằng thì cập nhật progress_json
            // Cập nhật progress_json, giá trị của progress_json là 1 mảng như sau: {"item": 1}
            if (totalItems >= itemCondition) {
              await models.PlayerMissions.update({
                is_completed: 1,
                completed_at: new Date(),
                // progress_json: JSON.stringify({ item: totalItems }),
                progress_json: { item: totalItems },
                completion_percent: 100
              }, {
                where: {
                  player_id: playerId,
                  mission_id: mission.id
                }
              });
            } else {
              await models.PlayerMissions.update({
                // progress_json: JSON.stringify({ item: totalItems }),
                progress_json: { item: totalItems },
                completion_percent: Math.round((1 / itemCondition) * 100)
              }, {
                where: {
                  player_id: playerId,
                  mission_id: mission.id
                }
              });
            }

          });

          break;
        
        case 'LEVEL_UP':
          missions.forEach(async mission => {
            const conditionJson = mission.condition_json;
            const levelCondition = conditionJson.level || 0;

            let playerMission = await models.PlayerMissions.findOne({
              where: {
                player_id: playerId,
                mission_id: mission.id
              }
            });

            if (!playerMission) {
              playerMission = await models.PlayerMissions.create({
                player_id: playerId,
                mission_id: mission.id,
                progress_json: { level: 1 },
                completion_percent: Math.round((1 / levelCondition) * 100),
                is_completed: 0,
                completed_at: null,
                reward_claimed: 0,
                claimed_at: null,
                created_at: new Date(),
              });
            }

            // kiểm tra trạng is_completed nếu bằng 0 thì tiếp tực thực hiện, nếu bằng 1 là hoàn thành rồi, thì dừng lại
            if (parseInt(playerMission.is_completed) === 1) {
              logger.info(`[missionService.processMissionProgress][LEVEL_UP] Mission ${mission.id} is already completed for player ${playerId}`);
              return;
            }

            const newLevel = parseInt(eventData.newLevel);
            // so sánh giá trị eventData.newLevel với levelCondition, nếu bằng thì cập nhật player mission và cập nhật progress_json, is_compoleted = 1, completed_at = current date, nếu kkhông bằng thì cập nhật progress_json
            // Cập nhật progress_json, giá trị của progress_json là 1 mảng như sau: {"level": 3}
            if (newLevel === parseInt(levelCondition)) {
              await models.PlayerMissions.update({
                is_completed: 1,
                completed_at: new Date(),
                // progress_json: JSON.stringify({ level: newLevel }),
                progress_json: { level: newLevel },
                completion_percent: 100
              }, {
                where: {
                  player_id: playerId,
                  mission_id: mission.id
                }
              });
            } else {
              await models.PlayerMissions.update({
                // progress_json: JSON.stringify({ level: newLevel }),
                progress_json: { level: newLevel },
                completion_percent: Math.round((newLevel / levelCondition) * 100)
              }, {
                where: {
                  player_id: playerId,
                  mission_id: mission.id
                }
              });
            }

          }); // End of missions.forEach

          break;
        
        case 'TOTAL_BET':
          missions.forEach(async mission => {
            const conditionJson = mission.condition_json;
            const totalBetCondition = conditionJson.total_bet_amount || 0;
            const betAmount = eventData.betAmount;
            let playerMission = await models.PlayerMissions.findOne({
              where: {
                player_id: playerId,
                mission_id: mission.id
              }
            });

            if (!playerMission) {
              playerMission = await models.PlayerMissions.create({
                player_id: playerId,
                mission_id: mission.id,
                progress_json: { total_bet_amount: betAmount },
                completion_percent: Math.round((betAmount / totalBetCondition) * 100),
                is_completed: 0,
                completed_at: null,
                reward_claimed: 0,
                claimed_at: null,
                created_at: new Date(),
              });
            }
            // kiểm tra trạng is_completed nếu bằng 0 thì tiếp tực thực hiện, nếu bằng 1 là hoàn thành rồi, thì dừng lại
            if (parseInt(playerMission.is_completed) === 1) {
              logger.info(`[missionService.processMissionProgress][TOTAL_BET] Mission ${mission.id} is already completed for player ${playerId}`);
              return;
            }
            // so sánh giá trị eventData.betAmount với totalBetCondition, nếu bằng thì cập nhật player mission và cập nhật progress_json, is_compoleted = 1, completed_at = current date, nếu kkhông bằng thì cập nhật progress_json
            // Cập nhật progress_json, giá trị của progress_json là 1 mảng như sau: {"total_bet_amount": 1000000}

            // totalBet sẽ bằng lấy giá trị cũ của totalBet cộng với giá trị của eventData.betAmount
            const totalBet = parseInt(playerMission.progress_json.total_bet_amount) + parseInt(betAmount);
            if (totalBet >= parseInt(totalBetCondition)) {
              await models.PlayerMissions.update({
                is_completed: 1,
                completed_at: new Date(),
                progress_json: {
                  total_bet_amount: totalBet
                },
                completion_percent: 100
              }, {
                where: {
                  player_id: playerId,
                  mission_id: mission.id
                }
              });
            } else {
              await models.PlayerMissions.update({
                progress_json: {
                  total_bet_amount: totalBet
                },
                completion_percent: Math.round((totalBet / totalBetCondition) * 100)
              }, {
                where: {
                  player_id: playerId,
                  mission_id: mission.id
                }
              });
            }; 
          }); // End of missions.forEach
          break;
        
        case 'MAKE_FRIEND':
          missions.forEach(async mission => {
            const conditionJson = mission.condition_json;
            const friendCountMission = conditionJson.friend_count || 0;
            logger.info(`[missionService.processMissionProgress][MAKE_FRIEND] friendCountMission: ${friendCountMission}, conditionJson: ${JSON.stringify(conditionJson)}`);

            let playerMission = await models.PlayerMissions.findOne({
              where: {
                player_id: playerId,
                mission_id: mission.id
              }
            });

            if (!playerMission) {
              playerMission = await models.PlayerMissions.create({
                player_id: playerId,
                mission_id: mission.id,
                progress_json: { friend_count: 1 },
                completion_percent: Math.round((1 / friendCountMission) * 100),
                is_completed: 0,
                completed_at: null,
                reward_claimed: 0,
                claimed_at: null,
                created_at: new Date(),
              });
            }

            // kiểm tra trạng is_completed nếu bằng 0 thì tiếp tực thực hiện, nếu bằng 1 là hoàn thành rồi, thì dừng lại
            if (parseInt(playerMission.is_completed) === 1) {
              logger.info(`[missionService.processMissionProgress][MAKE_FRIEND] Mission ${mission.id} is already completed for player ${playerId}`);
              return;
            }
            
            // Get danh sách bạn bè của người chơi theo playerId
            const friends = await models.Friends.findAll({
              where: {
                player_id: playerId,
                type: 1 // type = 2 là bạn bè, 1 là đã gửi lời mời kết bạn
              }
            });

            const friendCount = friends.length || 0;
            // so sánh giá trị friendCount với friendCountMission, nếu bằng thì cập nhật player mission và cập nhật progress_json, is_compoleted = 1, completed_at = current date, nếu kkhông bằng thì cập nhật progress_json
            // Cập nhật progress_json, giá trị của progress_json là 1 mảng như sau: {"friend_count": 3}
            if (friendCount === friendCountMission) {
              await models.PlayerMissions.update({
                is_completed: 1,
                completed_at: new Date(),
                progress_json: { friend_count: friendCount },
                completion_percent: 100
              }, {
                where: {
                  player_id: playerId,
                  mission_id: mission.id
                }
              });
            } else {
              await models.PlayerMissions.update({
                progress_json: { friend_count: friendCount },
                completion_percent: Math.round((friendCount / friendCountMission) * 100)
              }, {
                where: {
                  player_id: playerId,
                  mission_id: mission.id
                }
              });
            }

          });  

          break;
        
        // case 'PLAY_TABLES':
        //   // Update progress for missions related to leveling up
        //   break;
        default:
          logger.warn(`[missionService.processMissionProgress] Unknown event type: ${eventType}`);
      }
      
      // Return a mock result
      return {
        updated: [],
        completed: []
      };
    } catch (error) {
      logger.error('[missionService.processMissionProgress] Error in processMissionProgress:', error, ' -> mesage:', error.message, ' -> stack:', error.stack);
      throw error;
    }
  }
  
  /**
   * Claim reward for a completed mission
   * @param {number} playerId - The player's ID
   * @param {number} missionId - The mission ID
   * @returns {Promise<Object>} - Reward information
   */
  async claimReward(playerId, missionId) {
    const transaction = await sequelize.transaction();
    var self = this;
    try {
      // Get the mission
      const mission = await models.Missions.findByPk(missionId, { transaction });
      if (!mission) {
        throw new Error('Mission not found');
      }

      logger.info('[missionService.claimReward][Step 1] mission:', mission);

      // Get the player's mission progress
      const playerMission = await models.PlayerMissions.findOne({
        where: {
          player_id: playerId,
          mission_id: missionId
        },
        transaction
      });

      logger.info('[missionService.claimReward][Step 2] playerMission:', playerMission);
      
      // Check if the player has the mission and it's completed
      if (!playerMission) {
        throw new Error('Player has not started this mission');
      }
      
      if (playerMission.is_completed !== 1) {
        throw new Error('Mission is not completed yet');
      }
      
      if (playerMission.reward_claimed === 1) {
        throw new Error('Reward already claimed');
      }
      
      // Process the reward
      const reward = mission.reward_json;

      logger.info('[missionService.claimReward][Step 3] reward:', reward);
      
      // Update player mission record
      playerMission.reward_claimed = 1;
      playerMission.claimed_at = new Date();
      await playerMission.save({ transaction });
      
      // Log the reward claim
      await models.PlayerMissionLogs.create({
        player_id: playerId,
        mission_id: missionId,
        action_type: 'REWARDED',
        log_data: {
          reward: reward,
          claimed_at: playerMission.claimed_at
        }
      }, { transaction });

      // Commit the transaction
      await transaction.commit();

      // get player info, lấy ra balance hiện tại
      const { codePlayer, resPlayer } = await new Promise((resolve, reject) => {
        this.app.rpc.db.dbRemote.getPlayerById('*', playerId, (err, codePlayer, resPlayer) => {
            if (err) reject(err);
            else resolve({ codePlayer, resPlayer });
        });
      });
      
      if (codePlayer !== 200) {
        throw new Error(`Error getting player info`);
      }
      logger.info('[missionService.claimReward][Step 4] resPlayer:', resPlayer, ' -> codePlayer:', codePlayer);
      const currentBalance = resPlayer.balance;

      // cộng thưởng balance (nếu thưởng là coin)
      if (reward && reward.coins) {
        const newBalance = currentBalance + reward.coins;
        const updatePlayerData = {
          balance: newBalance
        };

        const dbService = this.app.get('dbService');
      
        try {
          await new Promise((resolve, reject) => {
            dbService.updatePlayer(playerId, updatePlayerData, (err, code, result) => {
              if (err) {
                logger.error(`[missionService.claimReward][Step 5] Error updatePlayer for player ${playerId}:`, err);
                // Không reject để tránh dừng transaction
                resolve({ success: false, code, result });
              } else {
                logger.info(`[missionService.claimReward][Step 5] updatePlayer successfully for player ${playerId} result:`, result);
                resolve({ success: true, code, result });
              }
            });
          });
          
          // Ghi transaction log bất kể updatePlayer thành công hay thất bại
          const transactionData = {
            player_id: playerId,
            amount: reward.coins,
            type: 'mission_reward',
            action: 'claim_reward',
            before_balance: currentBalance,
            after_balance: newBalance,
            meta: {
              mission_id: missionId,
              reward: reward,
              claimed_at: playerMission.claimed_at
            },
            description: `Claimed reward for mission ${missionId}`
          };
          
          logger.info('[missionService.claimReward][Step 6] transactionData:', transactionData);
          await new Promise(resolve => {
            dbService.createTransaction(transactionData, (err, code, result) => {
              if (err || code !== 200) {
                logger.error(`[missionService.claimReward][Step 6] Error recording transaction for player ${playerId}:`, err || result);
              } else {
                logger.info(`[missionService.claimReward][Step 6] Transaction recorded successfully for player ${playerId} result:`, result);
              }
              resolve();
            });
          });
        } catch (innerError) {
          logger.error('[missionService.claimReward] Inner error:', innerError);
          // Không throw để tránh dừng transaction chính
        }
      } // end if (reward && reward.coins)

      // // Commit the transaction
      // await transaction.commit();

      // Return reward information
      return new MissionRewardDTO(mission, playerMission, reward);
    } catch (error) {
      // Rollback the transaction in case of error
      await transaction.rollback();
      logger.error('[missionService.claimReward] Error in claimReward:', error, ' -> message:', error.message, ' -> stack:', error.stack);
      throw error;
    }
  }
  
  /**
   * Initialize missions for a new player
   * @param {number} playerId - The player's ID
   * @returns {Promise<void>}
   */
  async initializePlayerMissions(playerId) {
    try {
      // Get all active missions that should be assigned to new players
      const missions = await models.Missions.findAll({
        where: {
          is_active: 1,
          // Add any other conditions for initial missions
        }
      });
      
      // Create player mission records
      const playerMissions = missions.map(mission => ({
        player_id: playerId,
        mission_id: mission.id,
        progress_json: {},
        completion_percent: 0,
        is_completed: 0
      }));
      
      if (playerMissions.length > 0) {
        await models.PlayerMissions.bulkCreate(playerMissions);
      }
      
      logger.info(`Initialized ${playerMissions.length} missions for player ${playerId}`);
    } catch (error) {
      logger.error('Error in initializePlayerMissions:', error);
      throw error;
    }
  }
}

// module.exports = new MissionService();
module.exports = MissionService;
