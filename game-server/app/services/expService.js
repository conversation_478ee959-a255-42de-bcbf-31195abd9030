'use strict';

const logger = require('pomelo-logger').getLogger('pomelo', __filename);
const utils = require('../util/utils');
var consts = require('../consts/consts');
const CODE = require('../consts/code');
const LogTypes = require('../consts/logTypes');
const LogBoards = require('../consts/logBoards');
const levelsRank = require('../consts/levelRanks');
var messageService  = require('./messageService');

/**
 * Service xử lý hệ thống điểm kinh nghiệm (EXP) và tăng cấp
 */
class ExpService {
  constructor(app) {
    this.app = app;
  }

  /**
   * Cộng điểm kinh nghiệm cho người chơi
   * @param {Object} data - Dữ liệu cần thiết để cộng EXP
   * @param {number} data.playerId - ID của người chơi
   * @param {number} data.expValue - Số điểm EXP cần cộng
   * @param {string} data.source - Nguồn gốc của EXP (ví dụ: 'WIN_GAME', 'PLAY_TIME', 'BADGE_REWARD')
   * @param {Object} data.metadata - Thông tin bổ sung (tùy chọn)
   * @param {Function} cb - Callback function
   */
  addExp(data, cb) {
    const me = this;
    const { playerId, expValue, source, metadata = {} } = data;

    if (!playerId || !expValue || !source) {
      return cb(true, CODE.FAIL, 'Thiếu thông tin cần thiết');
    }

    // Lấy thông tin người chơi hiện tại
    this.app.rpc.db.dbRemote.getPlayerById('*', playerId, (playerErr, playerCode, playerData) => {
      if (playerErr || playerCode !== CODE.OK) {
        logger.error('[ExpService.addExp] Lỗi khi lấy thông tin người chơi:', playerErr);
        return cb(true, CODE.FAIL, 'Lỗi khi lấy thông tin người chơi');
      }

      // Tính toán giá trị EXP mới
      const currentExp = playerData?.exp || 0;
      const currentLevel = playerData?.level || 1;
      const newExp = currentExp + expValue;

      // Cập nhật EXP cho người chơi
      const updateData = {
        exp: newExp
      };

      logger.info(`[ExpService.addExp] Cập nhật EXP cho người chơi ${playerId} từ ${currentExp} thành ${newExp}`);

      // Cập nhật EXP trong database
      me.app.rpc.db.dbRemote.updatePlayer('*', playerId, updateData, async (updateErr, updateCode) => {
        if (updateErr || updateCode !== CODE.OK) {
          logger.error('[ExpService.addExp] Lỗi khi cập nhật EXP:', updateErr);
          return cb(true, CODE.FAIL, 'Lỗi khi cập nhật EXP');
        }

        // Tạo log cho việc cộng EXP => cộng kinh nghiệm thì không cần tạo logs
        // try {
        //   const logData = {
        //     type: LogTypes.SYSTEM,
        //     board: LogBoards.EXP_GAIN,
        //     userId: playerId,
        //     amount: expValue,
        //     dataRaw: {
        //       source: source,
        //       old_exp: currentExp,
        //       new_exp: newExp,
        //       timestamp: Math.floor(Date.now() / 1000),
        //       metadata: metadata
        //     }
        //   };

        //   me.app.get('sync').flush('playerSync.addLogs', playerId, logData);
        // } catch (logError) {
        //   logger.error(`[ExpService.addExp] Lỗi khi tạo log cho EXP:`, logError);
        // }

        // Kiểm tra xem người chơi có đủ điều kiện để lên cấp không
        me.checkLevelUp({
          playerId: playerId,
          currentExp: newExp,
          currentLevel: currentLevel
        }, (levelUpErr, levelUpCode, levelUpData) => {
          // Trả về kết quả cộng EXP, bao gồm cả thông tin lên cấp nếu có
          return cb(null, CODE.OK, {
            playerId: playerId,
            oldExp: currentExp,
            newExp: newExp,
            expAdded: expValue,
            source: source,
            levelUp: levelUpErr ? null : levelUpData
          });
        });
      });
    });
  }

  /**
   * Kiểm tra xem người chơi có đủ điều kiện để lên cấp không
   * @param {Object} data - Dữ liệu cần thiết để kiểm tra lên cấp
   * @param {number} data.playerId - ID của người chơi
   * @param {number} data.currentExp - Số điểm EXP hiện tại
   * @param {number} data.currentLevel - Cấp độ hiện tại
   * @param {Function} cb - Callback function
   */
  checkLevelUp(data, cb) {
    const me = this;
    const { playerId, currentExp, currentLevel } = data;

    // Tìm cấp độ tiếp theo
    const nextLevel = currentLevel + 1;
    const nextLevelData = levelsRank.find(level => level.level === nextLevel);

    // Nếu không có cấp độ tiếp theo hoặc chưa đủ EXP để lên cấp
    if (!nextLevelData || currentExp < nextLevelData.totalExp) {
      return cb(true, CODE.FAIL, 'Chưa đủ điều kiện lên cấp');
    }

    // Nếu đủ điều kiện lên cấp, tiến hành xử lý lên cấp
    me.processLevelUp({
      playerId: playerId,
      currentLevel: currentLevel,
      nextLevel: nextLevel,
      levelData: nextLevelData
    }, cb);
  }

  /**
   * Xử lý việc lên cấp và phát thưởng
   * @param {Object} data - Dữ liệu cần thiết để xử lý lên cấp
   * @param {number} data.playerId - ID của người chơi
   * @param {number} data.currentLevel - Cấp độ hiện tại
   * @param {number} data.nextLevel - Cấp độ tiếp theo
   * @param {Object} data.levelData - Thông tin cấp độ tiếp theo
   * @param {Function} cb - Callback function
   */
  processLevelUp(data, cb) {
    const me = this;
    const { playerId, currentLevel, nextLevel, levelData } = data;

    // Cập nhật cấp độ mới cho người chơi
    const updateData = {
      level: nextLevel
    };

    logger.info(`[ExpService.processLevelUp] Cập nhật cấp độ cho người chơi ${playerId} từ ${currentLevel} thành ${nextLevel}`);

    // Cập nhật cấp độ trong database
    me.app.rpc.db.dbRemote.updatePlayer('*', playerId, updateData, (updateErr, updateCode) => {
      if (updateErr || updateCode !== CODE.OK) {
        logger.error('[ExpService.processLevelUp] Lỗi khi cập nhật cấp độ:', updateErr);
        return cb(true, CODE.FAIL, 'Lỗi khi cập nhật cấp độ');
      }

      // Fire event lên cấp cho phần nhiệm vụ
      me.app.event.emit('playerLevelUp', {
        playerId: playerId,
        oldLevel: currentLevel,
        newLevel: nextLevel,
      });

      // Tạo log cho việc lên cấp
      try {
        const logData = {
          type: LogTypes.SYSTEM,
          board: LogBoards.LEVEL_UP,
          userId: playerId,
          amount: nextLevel,
          dataRaw: {
            old_level: currentLevel,
            new_level: nextLevel,
            level_title: levelData.title,
            level_rank: levelData.rank,
            timestamp: Math.floor(Date.now() / 1000)
          }
        };

        me.app.get('sync').flush('playerSync.addLogs', playerId, logData);
      } catch (logError) {
        logger.error(`[ExpService.processLevelUp] Lỗi khi tạo log cho lên cấp:`, logError);
      }

      // Xử lý phần thưởng lên cấp nếu có
      if (levelData.reward > 0) {
        me.grantLevelUpReward({
          playerId: playerId,
          level: nextLevel,
          reward: levelData.reward,
          levelData: levelData
        }, (rewardErr, rewardCode, rewardData) => {
          // Trả về kết quả lên cấp, bao gồm cả thông tin phần thưởng nếu có
          return cb(null, CODE.OK, {
            playerId: playerId,
            oldLevel: currentLevel,
            newLevel: nextLevel,
            levelTitle: levelData.title,
            levelTitleEn: levelData.title_en,
            levelRank: levelData.rank,
            reward: rewardErr ? null : rewardData
          });
        });
      } else {
        // Trả về kết quả lên cấp nếu không có phần thưởng
        return cb(null, CODE.OK, {
          playerId: playerId,
          oldLevel: currentLevel,
          newLevel: nextLevel,
          levelTitle: levelData.title,
          levelTitleEn: levelData.title_en,
          levelRank: levelData.rank,
          reward: null
        });
      }
    });
  }

  /**
   * Cấp phát phần thưởng lên cấp
   * @param {Object} data - Dữ liệu cần thiết để cấp phát phần thưởng
   * @param {number} data.playerId - ID của người chơi
   * @param {number} data.level - Cấp độ mới
   * @param {number} data.reward - Giá trị phần thưởng
   * @param {Object} data.levelData - Thông tin cấp độ
   * @param {Function} cb - Callback function
   */
  grantLevelUpReward(data, cb) {
    const me = this;
    const { playerId, level, reward, levelData } = data;

    // Lấy thông tin tài khoản hiện tại trước khi cập nhật
    // me.app.rpc.coin.coinRemote.getAccountInfo('*', { userId: playerId }, (getErr, getCode, accountInfo) => {
    me.app.rpc.db.dbRemote.getPlayerById('*', playerId, (getErr, getCode, accountInfo) => {
      if (getErr || getCode !== CODE.OK) {
        logger.error('[ExpService.grantLevelUpReward] Lỗi khi lấy thông tin tài khoản:', getErr);
        return cb(true, CODE.FAIL, 'Lỗi khi lấy thông tin tài khoản');
      }

      const currentBalance = accountInfo?.balance || 0;
      const newBalance = currentBalance + reward;

      logger.info(`[ExpService.grantLevelUpReward] Cập nhật CHIPS cho người chơi ${playerId} từ ${currentBalance} thành ${newBalance}`);

      // Chuẩn bị dữ liệu để cộng chips
      // const cashInData = {
      //   userId: playerId,
      //   amount: reward,
      //   description: `Phần thưởng lên cấp ${level}: ${levelData.title}`
      // };

      // // Gọi coinRemote để cộng chips
      // me.app.rpc.coin.coinRemote.cashIn('*', cashInData, (err, code, response) => {
      me.app.rpc.db.dbRemote.incrementPlayerStats(null, playerId, { balance: reward }, (err, code, response) => {
        if (err || code !== CODE.OK) {
          logger.error('[ExpService.grantLevelUpReward] Lỗi khi cộng chips:', err);
          return cb(true, CODE.FAIL, 'Lỗi khi cập nhật chips');
        }

        // Tạo log cho phần thưởng
        try {
          const logData = {
            type: LogTypes.SYSTEM,
            board: LogBoards.LEVEL_REWARD,
            userId: playerId,
            amount: reward,
            dataRaw: {
              level: level,
              level_title: levelData.title,
              reward_amount: reward,
              old_balance: currentBalance,
              new_balance: newBalance,
              timestamp: Math.floor(Date.now() / 1000)
            }
          };

          me.app.get('sync').flush('playerSync.addLogs', playerId, logData);

          me.app.get('sync').flush('playerSync.createTransaction', playerId, {
            player_id: playerId,
            amount: reward,
            before_balance: currentBalance,
            after_balance: newBalance,
            type: LogTypes.SYSTEM,
            action: LogBoards.LEVEL_REWARD,
            reference_id: level,
            reference_type: 'LEVEL_REWARD',
            meta: {
              level: level,
              level_title: levelData.title,
              reward_amount: reward,
              old_balance: currentBalance,
              new_balance: newBalance,
              timestamp: Math.floor(Date.now() / 1000)
            },
            description: `Phần thưởng lên cấp ${level}: ${levelData.title}`
          });
        } catch (logError) {
          logger.error(`[ExpService.grantLevelUpReward] Lỗi khi tạo log cho phần thưởng:`, logError);
        }

        // Gửi thông báo cập nhật thông tin người chơi
        me.notifyPlayerUpdate(playerId, {
          userId: playerId,
          amount: reward,
          dataRaw: {
            level: level,
            level_title: levelData.title,
            level_key: levelData.key || "",
            reward_amount: reward,
            old_balance: currentBalance,
            new_balance: newBalance,
            timestamp: Math.floor(Date.now() / 1000)
          }
        });

        // Trả về kết quả cấp phát phần thưởng
        return cb(null, CODE.OK, {
          playerId: playerId,
          level: level,
          rewardAmount: reward,
          oldBalance: currentBalance,
          newBalance: newBalance
        });
      }); // end incrementPlayerStats

    });
  }

  /**
   * Gửi thông báo cập nhật thông tin người chơi
   * @param {number} playerId - ID của người chơi
   * @param {Object} dataReward - Thông tin phần thưởng (tùy chọn)
   */
  notifyPlayerUpdate(playerId, dataReward) {
    logger.info(`[ExpService.notifyPlayerUpdate] Gửi thông báo cập nhật thông tin người chơi ${playerId} với dataReward: `, dataReward);
    const me = this;

    // Lấy thông tin người chơi mới nhất
    me.app.rpc.db.dbRemote.getPlayerById('*', playerId, (playerErr, playerCode, playerData) => {

      logger.info(`[ExpService.notifyPlayerUpdate] getPlayerById của ${playerId} -> playerErr: `, playerErr, ' -> playerCode: ', playerCode, ' -> playerData: ', playerData);

      if (playerErr || playerCode !== CODE.OK) {
        logger.error('[ExpService.notifyPlayerUpdate] Lỗi khi lấy thông tin người chơi:', playerErr);
        return;
      }

      // Cập nhật thông tin người chơi trong cache
      me.app.rpc.manager.userRemote.onUpdatePlayer('*', playerId, playerData, function (updatedUser) {
        logger.info(`[ExpService.notifyPlayerUpdate] onUpdatePlayer >> updatedUser:`, updatedUser);
        // Begin: Gửi thông báo cập nhật thông tin người chơi
        // ------------------------------------------------------------------------------------------------
        // const playerMsg = {
        //   ...playerData,
        //   balance: accountInfo.balance
        // };
        logger.info(`[ExpService.notifyPlayerUpdate] playerData:`, playerData);

        messageService.pushMessageByUid(
          playerId,
          consts.GAME.ROUTER.UPDATE_MYSELF,
          {
            player: playerData, // playerMsg,
            reward: dataReward,
            type: CODE.USER.LEVEL_REWARD
          },
          CODE.USER.LEVEL_REWARD
        ).then(() => {
            logger.info(`[ExpService.notifyPlayerUpdate] Đã gửi thông báo onUpdateMyself cho người chơi ${playerId}`);
        }).catch((pushError) => {
            logger.error(`[ExpService.notifyPlayerUpdate] Lỗi khi gửi thông báo cho người chơi ${playerId}:`, pushError.message || pushError);
        });
        // ------------------------------------------------------------------------------------------------
        // End: Gửi thông báo cập nhật thông tin người chơi
      }); // end onUpdatePlayer

      // // Lấy thông tin tài khoản mới nhất => không cần lấy thông tin chip (balance) từ service coin nữa
      // me.app.rpc.coin.coinRemote.getAccountInfo('*', { userId: playerId }, (getErr, getCode, accountInfo) => {
      //   logger.info(`[ExpService.notifyPlayerUpdate] getAccountInfo của ${playerId} -> getErr: `, getErr, ' -> getCode: ', getCode, ' -> accountInfo: ', accountInfo);
      //   if (getErr || getCode !== CODE.OK) {
      //     logger.error('[ExpService.notifyPlayerUpdate] Lỗi khi lấy thông tin tài khoản:', getErr);
      //     return;
      //   }
      // });
    });
  }

  /**
   * Tính toán EXP dựa trên thời gian chơi
   * @param {Object} data - Dữ liệu cần thiết để tính toán EXP
   * @param {number} data.playerId - ID của người chơi
   * @param {number} data.playTimeMinutes - Thời gian chơi tính bằng phút
   * @param {Function} cb - Callback function
   */
  calculatePlayTimeExp(data, cb) {
    const { playerId, playTimeMinutes } = data;
    let expValue = 0;

    // Tính toán EXP dựa trên thời gian chơi theo quy tắc
    if (playTimeMinutes >= 5 && playTimeMinutes < 10) {
      expValue = 5;
    } else if (playTimeMinutes >= 10 && playTimeMinutes < 15) {
      expValue = 10;
    } else if (playTimeMinutes >= 15 && playTimeMinutes < 20) {
      expValue = 10;
    } else if (playTimeMinutes >= 20 && playTimeMinutes < 30) {
      expValue = 10;
    } else if (playTimeMinutes >= 30 && playTimeMinutes < 60) {
      expValue = 20;
    } else if (playTimeMinutes >= 60 && playTimeMinutes < 90) {
      expValue = 60;
    } else if (playTimeMinutes >= 90 && playTimeMinutes < 120) {
      expValue = 40;
    } else if (playTimeMinutes >= 120) {
      // Trên 2 tiếng, mỗi tiếng được 20 EXP
      const additionalHours = Math.floor((playTimeMinutes - 120) / 60);
      expValue = 40 + (additionalHours * 20);
    }

    if (expValue > 0) {
      this.addExp({
        playerId: playerId,
        expValue: expValue,
        source: 'PLAY_TIME',
        metadata: {
          play_time_minutes: playTimeMinutes
        }
      }, cb);
    } else {
      cb(null, CODE.OK, {
        playerId: playerId,
        expValue: 0,
        message: 'Không đủ thời gian để nhận EXP'
      });
    }
  }

  /**
   * Tính toán EXP dựa trên kết quả ván bài
   * @param {Object} data - Dữ liệu cần thiết để tính toán EXP
   * @param {number} data.playerId - ID của người chơi
   * @param {boolean} data.isWin - Kết quả ván bài (thắng/thua)
   * @param {number} data.playerCount - Số người chơi trong ván
   * @param {number} data.smallBlind - Giá trị small blind của bàn
   * @param {number} data.playerLevel - Cấp độ hiện tại của người chơi
   * @param {Function} cb - Callback function
   */
  calculateGameResultExp(data, cb) {
    const { playerId, isWin, playerCount, smallBlind, playerLevel } = data;
    let expValue = 0;

    // Kiểm tra nếu là người chơi mới (cấp 1-3) và thua, không giảm EXP
    if (!isWin && playerLevel <= 3) {
      return cb(null, CODE.OK, {
        playerId: playerId,
        expValue: 0,
        message: 'Người chơi mới không bị giảm EXP khi thua'
      });
    }

    // Tính toán EXP dựa trên kết quả ván bài và giá trị small blind
    if (isWin) {
      // Thắng bài
      if (smallBlind < 10000000) { // 10M
        expValue = playerCount * 2;
      } else {
        expValue = playerCount * 4;
      }
    } else {
      // Thua bài
      if (smallBlind < 10000000) { // 10M
        expValue = -1;
      } else {
        expValue = -2;
      }
    }

    this.addExp({
      playerId: playerId,
      expValue: expValue,
      source: isWin ? 'WIN_GAME' : 'LOSE_GAME',
      metadata: {
        player_count: playerCount,
        small_blind: smallBlind,
        is_win: isWin
      }
    }, cb);
  }
}

module.exports = function(app) {
  return new ExpService(app);
};
