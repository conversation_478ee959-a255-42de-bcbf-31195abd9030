# Tổng kết phát triển tính năng giải đấu SNG (Sit and Go)

## 1. Tổ<PERSON> quan

Tính năng giải đấu SNG (Sit and Go) là một hình thức giải đấu poker với số lượng người chơi cố định, mứ<PERSON> cư<PERSON>c tăng dần theo thời gian, và phần thưởng được phân phối dựa trên thứ hạng cuối cùng của người chơi. Tính năng này được phát triển dựa trên hệ thống chơi tự do hiện có, với các điều chỉnh cần thiết để hỗ trợ cơ chế giải đấu.

## 2. C<PERSON>u trúc cơ sở dữ liệu

### 2.1. <PERSON><PERSON><PERSON> bảng dữ liệu

1. **sng_tournaments**: <PERSON><PERSON><PERSON> trữ thông tin giải đấu

   - `id`: ID giải đấu
   - `code`: <PERSON>ã giải đấu
   - `status`: Trạng thái giải đấu (WAITING, READY, IN_PROGRESS, ENDED)
   - `player_capacity`: Số lượng người chơi tối đa
   - `buy_in`: Phí tham gia (chips)
   - `fee`: Phí dịch vụ (chips)
   - `reward_pool`: Tổng giải thưởng
   - `created_at`: Thời điểm tạo
   - `started_at`: Thời điểm bắt đầu
   - `ended_at`: Thời điểm kết thúc

2. **sng_tournament_players**: Lưu trữ thông tin người chơi trong giải đấu

   - `id`: ID bản ghi
   - `tournament_id`: ID giải đấu
   - `player_id`: ID người chơi
   - `seat_number`: Số ghế
   - `initial_chips`: Số chips ban đầu
   - `current_chips`: Số chips hiện tại
   - `status`: Trạng thái (ACTIVE, ELIMINATED, WINNER)
   - `eliminated_at_hand`: Vòng bị loại
   - `rank`: Thứ hạng
   - `joined_at`: Thời điểm tham gia

3. **sng_blind_levels**: Lưu trữ thông tin mức blind

   - `id`: ID bản ghi
   - `tournament_id`: ID giải đấu
   - `level_number`: Số thứ tự mức blind
   - `small_blind`: Mức small blind
   - `big_blind`: Mức big blind
   - `ante`: Mức ante
   - `duration_seconds`: Thời gian kéo dài (giây)

4. **sng_hand_histories**: Lưu trữ lịch sử các vòng chơi

   - `id`: ID bản ghi
   - `tournament_id`: ID giải đấu
   - `hand_number`: Số thứ tự vòng chơi
   - `dealer_seat`: Vị trí dealer
   - `actions_json`: Các hành động trong vòng chơi
   - `pot`: Tổng pot
   - `winner_seat`: Vị trí người thắng
   - `created_at`: Thời điểm tạo

5. **sng_rewards**: Lưu trữ thông tin giải thưởng

   - `id`: ID bản ghi
   - `tournament_id`: ID giải đấu
   - `player_id`: ID người chơi
   - `rank`: Thứ hạng
   - `reward_amount`: Số tiền thưởng
   - `rewarded_at`: Thời điểm nhận thưởng

6. **sng_tournament_logs**: Lưu trữ lịch sử các hành động trong giải đấu
   - `id`: ID bản ghi
   - `tournament_id`: ID giải đấu
   - `player_id`: ID người chơi (có thể null nếu là hành động hệ thống)
   - `action_type`: Loại hành động (SNG_REGISTER, SNG_REFUND, SNG_REWARD, SNG_BLIND_INCREASE, SNG_PLAYER_ELIMINATED, SNG_TOURNAMENT_END)
   - `data`: Dữ liệu chi tiết về hành động (JSON)
   - `amount`: Số tiền liên quan đến hành động (nếu có)
   - `created_at`: Thời điểm ghi log

### 2.2. Các DTO (Data Transfer Objects)

1. **SngTournamentDTO**: Đối tượng truyền dữ liệu giải đấu
2. **SngTournamentPlayerDTO**: Đối tượng truyền dữ liệu người chơi
3. **SngBlindLevelDTO**: Đối tượng truyền dữ liệu mức blind
4. **SngRewardDTO**: Đối tượng truyền dữ liệu giải thưởng

## 3. Các thành phần chính

### 3.1. SngTournamentService

Dịch vụ quản lý giải đấu SNG, bao gồm các chức năng:

1. **Quản lý giải đấu**

   - Tạo giải đấu mới
   - Đăng ký người chơi
   - Bắt đầu giải đấu
   - Kết thúc giải đấu

2. **Quản lý mức blind**

   - Tăng mức blind theo thời gian
   - Thông báo thay đổi mức blind

3. **Quản lý người chơi**

   - Xử lý người chơi bị loại
   - Xác định người chiến thắng
   - Phân phối giải thưởng

4. **Thông báo sự kiện**
   - Thông báo trạng thái giải đấu
   - Thông báo người chơi bị loại
   - Thông báo kết quả giải đấu

### 3.2. SngTournamentHandler

Xử lý các yêu cầu từ client liên quan đến giải đấu SNG:

1. **getTournaments**: Lấy danh sách giải đấu được tổ chức theo loại (5_PLAYERS, 9_PLAYERS) và cấp độ (BEGINNER, INTERMEDIATE, ADVANCED, PRO), kèm theo tổng số người chơi đã đăng ký cho mỗi loại và cấp độ
2. **getTournament**: Lấy thông tin chi tiết giải đấu
3. **createTournament**: Tạo giải đấu mới
4. **registerTournament**: Đăng ký tham gia giải đấu theo loại và cấp độ, hệ thống tự động tìm giải đấu phù hợp
5. **leaveTournament**: Rời khỏi giải đấu, hệ thống tự động xác định giải đấu mà người chơi đã đăng ký
6. **execute**: Thực hiện hành động trong giải đấu

### 3.3. Tích hợp với hệ thống hiện có

1. **Table.js**: Bổ sung hỗ trợ cho ante và theo dõi số vòng chơi
2. **Player.js**: Xử lý loại bỏ người chơi trong giải đấu
3. **Game.js**: Bổ sung thuộc tính handNumber để theo dõi vòng chơi

## 4. Luồng xử lý chính

### 4.1. Tạo và đăng ký giải đấu

1. Admin tạo giải đấu mới với các thông số cấu hình (hoặc hệ thống tự động tạo khi khởi động)
2. Người chơi chọn loại giải đấu (5_PLAYERS hoặc 9_PLAYERS) và cấp độ (BEGINNER, INTERMEDIATE, ADVANCED, PRO)
3. Người chơi đăng ký tham gia giải đấu bằng cách gọi hàm registerTournament với tham số type và level
4. Hệ thống tự động tìm giải đấu phù hợp với loại và cấp độ được chỉ định, hoặc tạo mới nếu cần
5. Khi đủ số lượng người chơi, giải đấu chuyển sang trạng thái READY
6. Hệ thống tự động bắt đầu giải đấu

### 4.2. Tiến trình giải đấu

1. Người chơi được phân bổ vào bàn chơi
2. Mức blind tăng theo thời gian định sẵn
3. Người chơi bị loại khi hết chips
4. Thứ hạng được xác định dựa trên thời điểm bị loại
5. Khi chỉ còn một người chơi, giải đấu kết thúc

### 4.3. Kết thúc giải đấu và phân phối giải thưởng

1. Người chơi cuối cùng được xác định là người chiến thắng
2. Hệ thống phân phối giải thưởng dựa trên thứ hạng
3. Thông báo kết quả giải đấu cho tất cả người chơi
4. Cập nhật số dư của người chơi

## 5. Cải tiến và tối ưu hóa

### 5.1. Xử lý loại bỏ người chơi

- Kiểm tra người chơi đã bị loại trước đó chưa
- Xác định người chiến thắng khi chỉ còn một người chơi
- Ghi log chi tiết về quá trình loại bỏ người chơi

### 5.2. Phân phối giải thưởng

- Điều chỉnh tỷ lệ phân phối dựa trên số lượng người chơi
- Xử lý đúng các trường hợp đặc biệt (1 hoặc 2 người chơi)
- Ghi log chi tiết về quá trình phân phối giải thưởng

### 5.3. Xử lý thứ hạng người chơi

- Sắp xếp người chơi theo trạng thái (ACTIVE trước, ELIMINATED sau)
- Tính toán thứ hạng dựa trên số người chơi còn hoạt động
- Ghi log chi tiết về thứ hạng người chơi

### 5.4. Xử lý tăng mức blind

- Lưu trữ mức blind hiện tại trong bộ nhớ
- Kiểm tra số người chơi còn hoạt động trước khi tăng mức blind
- Ghi log chi tiết về quá trình tăng mức blind

### 5.5. Tự động tạo giải đấu mới

- Tự động tạo giải đấu mới khi giải đấu hiện tại đã đủ người và bắt đầu
- Đảm bảo luôn có ít nhất một giải đấu ở trạng thái WAITING cho mỗi loại và cấp độ
- Tạo giải đấu mới với cùng cấu hình (số người chơi, mức buy-in, phí) như giải đấu đã bắt đầu
- Kiểm tra và tạo giải đấu cho tất cả các loại và cấp độ khi khởi động hệ thống
- Giới hạn số lượng giải đấu tối đa cho mỗi loại và cấp độ để tránh tạo quá nhiều giải đấu
- Kiểm tra số lượng giải đấu hiện có trước khi tạo giải đấu mới để tránh trùng lặp
- Sử dụng cờ đánh dấu để đảm bảo mỗi worker chỉ tạo giải đấu ban đầu một lần
- Sử dụng khóa tư vấn MySQL (MySQL advisory lock) để đảm bảo chỉ một worker có thể tạo giải đấu tại một thời điểm
- Thêm độ trễ ngẫu nhiên để tránh nhiều worker cùng cố gắng tạo giải đấu đồng thời
- Ghi log chi tiết với thông tin worker ID để dễ dàng theo dõi và gỡ lỗi
- Cải thiện xử lý lỗi để tránh crash khi có lỗi xảy ra trong quá trình tạo giải đấu
- Tạo chính xác 8 giải đấu ban đầu (1 cho mỗi loại và cấp độ) khi khởi động hệ thống lần đầu

### 5.6. Cải thiện luồng thoát khỏi giải đấu

- Cải tiến hàm `leaveTournament` để không cần tham số `tournamentId`
- Hệ thống tự động kiểm tra xem người chơi hiện tại có đang đăng ký chờ giải đấu nào không
- Sử dụng `isPlayerInTournament` và `getTournamentForPlayer` để xác định giải đấu mà người chơi đã đăng ký
- Trả về thông báo lỗi phù hợp nếu người chơi không đăng ký giải đấu nào
- Cải thiện hàm `handlePlayerLeave` để trả về thông tin chi tiết về kết quả rời giải đấu
- Bổ sung thông tin về số tiền hoàn lại và số dư người chơi sau khi rời giải đấu
- Cập nhật tài liệu để phản ánh các thay đổi trong luồng thoát khỏi giải đấu

### 5.7. Sửa lỗi push message format

- Sửa lỗi push message trong SNG Tournament events không đúng format so với bàn chơi thường
- Thay thế `channel.pushMessage()` bằng `channelService.pushMessageByUids()` để đảm bảo format đúng
- Thêm helper functions `getChannelReceivers()` và `sendPushMessage()` để tối ưu code và giảm trùng lặp
- Cải thiện xử lý lỗi và logging cho các push message events
- Đảm bảo tất cả SNG Tournament events được gửi với format: `groups: {"connector-server-3":[561]}`
- Sửa lỗi cho các events: SNG_TOURNAMENT_JOIN, SNG_TOURNAMENT_STATUS, SNG_TOURNAMENT_BLIND_UPDATE, SNG_TOURNAMENT_PLAYER_ELIMINATED, SNG_TOURNAMENT_RESULT
- Tối ưu hóa code bằng cách sử dụng helper functions thay vì lặp lại logic push message

## 6. Các sự kiện và thông báo

1. **SNG_TOURNAMENT_LIST**: Danh sách giải đấu được tổ chức theo loại (5_PLAYERS, 9_PLAYERS) và cấp độ (BEGINNER, INTERMEDIATE, ADVANCED, PRO), kèm theo tổng số người chơi đã đăng ký cho mỗi loại và cấp độ
2. **SNG_TOURNAMENT_JOIN**: Tham gia giải đấu
3. **SNG_TOURNAMENT_STATUS**: Cập nhật trạng thái giải đấu
4. **SNG_TOURNAMENT_BLIND_UPDATE**: Cập nhật mức blind
5. **SNG_TOURNAMENT_PLAYER_ELIMINATED**: Thông báo người chơi bị loại
6. **SNG_TOURNAMENT_RESULT**: Kết quả giải đấu

## 7. Ghi log và theo dõi

1. **SNG_REGISTER**: Đăng ký giải đấu
2. **SNG_REFUND**: Hoàn phí giải đấu
3. **SNG_REWARD**: Nhận thưởng giải đấu
4. **SNG_BLIND_INCREASE**: Tăng mức blind
5. **SNG_PLAYER_ELIMINATED**: Người chơi bị loại
6. **SNG_TOURNAMENT_END**: Kết thúc giải đấu

## 8. Cấu hình và tùy chỉnh

Các thông số cấu hình được định nghĩa trong:

1. **sngConsts.js**: Hằng số cho giải đấu SNG

   - Tỷ lệ phân phối giải thưởng
   - Cấu trúc mức blind mặc định
   - Số chips ban đầu
   - Thời gian tăng mức blind

2. **sngTournaments.json**: Cấu hình giải đấu
   - Các loại giải đấu (5 người và 9 người)
   - Các cấp độ giải đấu (Beginner, Intermediate, Advanced, Pro)
   - Phí tham gia và phí dịch vụ cho từng loại giải đấu
   - Số lượng người chơi cho từng loại giải đấu
   - Cấu trúc mức blind tùy chỉnh
   - Tỷ lệ phân phối giải thưởng tùy chỉnh

File `sngTournaments.json` được đọc khi khởi động dịch vụ và được sử dụng để:

- Tạo giải đấu mới với các thông số cấu hình phù hợp
- Xác định mức blind và thời gian tăng mức blind
- Xác định tỷ lệ phân phối giải thưởng
- Cung cấp thông tin mô tả cho từng loại giải đấu

## 9. Kiểm thử

File kiểm thử **sngTournamentTest.js** bao gồm:

1. Tạo giải đấu thử nghiệm
2. Tạo người chơi thử nghiệm
3. Đăng ký người chơi vào giải đấu
4. Bắt đầu giải đấu
5. Mô phỏng kết thúc giải đấu
6. Phân phối giải thưởng

## 10. Tài liệu

1. **SNG_TOURNAMENT.md**: Tài liệu tổng quan về tính năng
2. **SNG_TOURNAMENT_SUMMARY.md**: Tổng kết quá trình phát triển

## 11. Logic tiền ảo và vị trí ngồi

### 11.1. Vị trí ngồi (seat_number)

- **Thiết kế đúng**: `seat_number = null` khi đăng ký là bình thường
- **Gán vị trí**: Chỉ được gán khi tournament bắt đầu (status = IN_PROGRESS)
- **Logic gán**: Tuần tự theo thứ tự index của người chơi (0, 1, 2, ...)
- **Lý do**: Vị trí ngồi chỉ có ý nghĩa khi bàn chơi được tạo

### 11.2. Hệ thống tiền ảo

- **Tiền thật**: `player.balance` - bị trừ buy_in + fee khi đăng ký
- **Tiền ảo**: `current_chips` - 100M chips để chơi trong tournament
- **Tách biệt hoàn toàn**: Tiền ảo không ảnh hưởng tiền thật
- **An toàn**: Chỉ giải thưởng mới được cộng vào tiền thật

### 11.3. Luồng tiền chi tiết

1. **Đăng ký**: Trừ tiền thật (buy_in + fee), cấp 100M chips ảo
2. **Chơi**: Sử dụng chips ảo, tiền thật không đổi
3. **Kết thúc**: Giải thưởng từ reward_pool cộng vào tiền thật

## 12. Kết luận

Tính năng giải đấu SNG đã được phát triển thành công, bao gồm đầy đủ các chức năng cần thiết từ đăng ký, tham gia, thi đấu đến phân phối giải thưởng. Tính năng này tích hợp tốt với hệ thống chơi tự do hiện có, đồng thời bổ sung các cơ chế đặc thù của giải đấu như tăng mức blind, xác định thứ hạng và phân phối giải thưởng.

Các cải tiến đã được thực hiện để đảm bảo tính chính xác và đáng tin cậy của hệ thống, đặc biệt là trong việc tính toán thứ hạng, phân phối giải thưởng và xử lý các trường hợp đặc biệt. Hệ thống ghi log chi tiết giúp dễ dàng theo dõi và khắc phục sự cố nếu có.

Việc sửa lỗi push message format đảm bảo rằng tất cả thông báo SNG Tournament được gửi đến người chơi một cách chính xác và nhất quán với hệ thống bàn chơi thường, cải thiện đáng kể trải nghiệm người dùng và tính ổn định của hệ thống.

**Logic tiền ảo và vị trí ngồi đã được thiết kế đúng và an toàn**, đảm bảo tách biệt hoàn toàn giữa tiền thật và tiền ảo trong tournament, đồng thời vị trí ngồi được gán một cách hợp lý khi tournament bắt đầu.
