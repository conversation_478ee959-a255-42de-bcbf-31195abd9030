var logger = require('pomelo-logger').getLogger('sng-log', __filename);
var consts = require('../consts/consts');
var CODE = require('../consts/code');
var sngConsts = require('../consts/sngConsts');
var logBoards = require('../consts/logBoards');
var uuid = require('node-uuid');
var async = require('async');
var _ = require('underscore');
// var models = require('../models');
const { models, Sequelize, sequelize } = require("../models");
var moment = require('moment');
var { SngTournamentDTO, SngTournamentPlayerDTO, SngBlindLevelDTO, SngRewardDTO } = require('../domain/entity/sngTournament');
var Table = require('../game/table');
var Game = require('../game/game');
var fs = require('fs');
var path = require('path');
var Op = Sequelize.Op;

/**
 * SNG Tournament Service
 *
 * Handles SNG tournament creation, registration, and management
 */
var SngTournamentService = function(app, opts) {
    opts = opts || {};
    this.app = app;
    this.tournaments = {}; // Active tournaments in memory
    this.tournamentTables = {}; // Tables for active tournaments
    this.playerTournaments = {}; // Map of player ID to tournament ID
    this.blindTimers = {}; // Timers for blind increases
    this.dbService = this.app.get('dbService');
    // this.channelService = this.app.get('channelService');
    this.tournamentConfigs = {}; // Tournament configurations from JSON file

    // Log warning if channelService is not available
    // if (!this.channelService) {
    //     logger.warn('Channel service not available in SNG Tournament Service. Notifications will be disabled.');
    // }

    // Load tournament configurations
    this.loadTournamentConfigs();

    // Initialize the service
    this.init();
};

/**
 * Load tournament configurations from JSON file
 */
SngTournamentService.prototype.loadTournamentConfigs = function() {
    try {
        // Read the configuration file
        var configPath = path.join(process.cwd(), 'config', 'data', 'sngTournaments.json');
        var configData = fs.readFileSync(configPath, 'utf8');
        var config = JSON.parse(configData);

        // Store the configurations
        this.tournamentConfigs = config;

        // Map tournaments by ID for easy access
        this.tournamentConfigsById = {};
        if (config.tournaments && Array.isArray(config.tournaments)) {
            config.tournaments.forEach(tournament => {
                this.tournamentConfigsById[tournament.id] = tournament;
            });
        }

        // Store blind structure
        this.blindStructure = config.blind_structure || sngConsts.DEFAULT_BLIND_STRUCTURE;

        // Store reward distribution
        if (config.reward_distribution) {
            sngConsts.REWARD_DISTRIBUTION.FIRST_PLACE = config.reward_distribution.first_place || 50;
            sngConsts.REWARD_DISTRIBUTION.SECOND_PLACE = config.reward_distribution.second_place || 30;
            sngConsts.REWARD_DISTRIBUTION.THIRD_PLACE = config.reward_distribution.third_place || 20;
        }

        logger.info('SNG Tournament configurations loaded successfully');
    } catch (err) {
        logger.error('Error loading SNG Tournament configurations:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
        // Use default configurations if file cannot be loaded
        this.tournamentConfigs = {
            tournaments: [],
            blind_structure: sngConsts.DEFAULT_BLIND_STRUCTURE,
            reward_distribution: {
                first_place: sngConsts.REWARD_DISTRIBUTION.FIRST_PLACE,
                second_place: sngConsts.REWARD_DISTRIBUTION.SECOND_PLACE,
                third_place: sngConsts.REWARD_DISTRIBUTION.THIRD_PLACE
            }
        };
    }
};

module.exports = SngTournamentService;

/**
 * Initialize the service
 */
SngTournamentService.prototype.init = function() {
    var self = this;

    // Flag to track if we've already created initial tournaments
    this.initialTournamentsCreated = false;

    logger.info('Initializing SNG Tournament Service');

    // Load active tournaments from database with retry mechanism
    this.loadActiveTournamentsWithRetry(3, function(err) {
        if (err) {
            logger.error('Failed to load active tournaments after retries:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
        } else {
            logger.info('SNG Tournament Service initialized successfully');

            // Check if initialization has already been done
            self.checkInitializationStatus(function(isInitialized) {
                if (isInitialized) {
                    logger.info('SNG tournaments have already been initialized. Skipping initialization.');
                    return;
                }

                // Add a random delay to better avoid race conditions between servers
                var randomDelay = Math.floor(Math.random() * 5000) + 1000; // 1-6 seconds
                logger.info('Will check for tournaments after ' + randomDelay + 'ms delay');

                setTimeout(function() {
                    // Double-check initialization status after the delay
                    self.checkInitializationStatus(function(isInitialized) {
                        if (isInitialized) {
                            logger.info('SNG tournaments have been initialized during delay. Skipping initialization.');
                            return;
                        }

                        // Only create initial tournaments if not already done
                        if (!self.initialTournamentsCreated) {
                            self.initialTournamentsCreated = true;
                            // Ensure there are available tournaments for each type and level
                            self.ensureAvailableTournaments();
                        }
                    });
                }, randomDelay);
            });
        }
    });
};

/**
 * Load active tournaments from database with retry mechanism
 */
SngTournamentService.prototype.loadActiveTournamentsWithRetry = function(maxRetries, cb, currentRetry) {
    var self = this;
    currentRetry = currentRetry || 0;

    // Try to load tournaments
    this.loadActiveTournaments(function(err) {
        if (err) {
            // If error and retries left, try again after a delay
            if (currentRetry < maxRetries) {
                logger.warn('Failed to load active tournaments (retry ' + (currentRetry + 1) + '/' + maxRetries + '):', err, ' -> message: ', err.message, ' -> stack: ', err.stack);

                // Exponential backoff delay
                var delay = Math.pow(2, currentRetry) * 1000;
                setTimeout(function() {
                    self.loadActiveTournamentsWithRetry(maxRetries, cb, currentRetry + 1);
                }, delay);
            } else {
                // Max retries reached
                if (cb) cb(err);
            }
        } else {
            // Success
            if (cb) cb(null);
        }
    });
};

/**
 * Load active tournaments from database
 */
SngTournamentService.prototype.loadActiveTournaments = function(cb) {
    var self = this;

    // Use a transaction to ensure consistent database state
    sequelize.transaction({ readOnly: true }).then(function(t) {
        // Find tournaments that are not ended
        return models.SngTournament.findAll({
            where: {
                status: {
                    [Op.ne]: 'ENDED'
                }
            },
            include: [
                {
                    model: models.SngTournamentPlayer,
                    as: 'players',
                    include: [
                        {
                            model: models.Player,
                            as: 'player'
                        }
                    ]
                },
                {
                    model: models.SngBlindLevel,
                    as: 'blindLevels'
                }
            ],
            transaction: t
        }).then(function(tournaments) {
            if (tournaments && tournaments.length > 0) {
                tournaments.forEach(function(tournament) {
                    self.tournaments[tournament.id] = tournament;

                    // If tournament is in progress, recreate the table
                    if (tournament.status === 'IN_PROGRESS') {
                        self.recreateTournamentTable(tournament);
                    }

                    // Map players to tournaments
                    if (tournament.players && tournament.players.length > 0) {
                        tournament.players.forEach(function(player) {
                            self.playerTournaments[player.player_id] = tournament.id;
                        });
                    }

                    // Restart blind timer if tournament is in progress
                    if (tournament.status === 'IN_PROGRESS') {
                        self.startBlindTimer(tournament.id);
                    }
                });
            }

            // Commit the read-only transaction
            return t.commit().then(function() {
                if (cb) cb(null);
            });
        }).catch(function(err) {
            // Rollback on error
            t.rollback();
            logger.error('Error loading active tournaments:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
            if (cb) cb(err);
        });
    }).catch(function(err) {
        logger.error('Error starting transaction for loading active tournaments:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
        if (cb) cb(err);
    });
};

/**
 * Recreate tournament table for an active tournament
 */
SngTournamentService.prototype.recreateTournamentTable = function(tournament) {
    // Implementation will depend on how tables are managed in the system
    // This is a placeholder for the actual implementation
    logger.info('Recreating tournament table for tournament ID:', tournament.id);
};

/**
 * Get all available tournaments
 * Organized by type (5_PLAYERS, 9_PLAYERS) and level (BEGINNER, INTERMEDIATE, ADVANCED, PRO)
 */
SngTournamentService.prototype.getTournaments = function(cb) {
    // Find all tournaments that are not ended
    models.SngTournament.findAll({
        where: {
            status: {
                [Op.ne]: 'ENDED'
            },
            // And player_capacity > 0
            player_capacity: {
                [Op.gt]: 0
            }
        },
        include: [
            {
                model: models.SngTournamentPlayer,
                as: 'players',
                attributes: ['id', 'player_id', 'status']
            },
            {
                model: models.SngBlindLevel,
                as: 'blindLevels',
                where: {
                    level_number: 1
                },
                required: false
            }
        ]
    }).then(function(tournaments) {
        var tournamentDTOs = [];
        const sng5Value = 5;
        const sng9Value = 9;
        const firstPlace = sngConsts.REWARD_DISTRIBUTION.FIRST_PLACE;
        const secondPlace = sngConsts.REWARD_DISTRIBUTION.SECOND_PLACE;
        const thirdPlace = sngConsts.REWARD_DISTRIBUTION.THIRD_PLACE;
        // Create a structure to organize tournaments by type and level
        var organizedTournaments = {
            types: [
                {
                    type: sngConsts.TOURNAMENT_TYPE.SNG_5,
                    name: "5 Players",
                    levels: [
                        {
                            level: sngConsts.TOURNAMENT_LEVEL.BEGINNER, name: "Beginner", /*tournaments: [],*/ total_players: 0,
                            buy_in: sngConsts.TOURNAMENT_FEES_5.BEGINNER.BUY_IN,
                            fee: sngConsts.TOURNAMENT_FEES_5.BEGINNER.FEE,
                            reward_distribution: {
                                first_place: (sngConsts.TOURNAMENT_FEES_5.BEGINNER.BUY_IN * sng5Value)*(firstPlace/100),
                                second_place: (sngConsts.TOURNAMENT_FEES_5.BEGINNER.BUY_IN * sng5Value)*(secondPlace/100),
                                third_place: (sngConsts.TOURNAMENT_FEES_5.BEGINNER.BUY_IN * sng5Value)*(thirdPlace/100)
                            }
                        },
                        {
                            level: sngConsts.TOURNAMENT_LEVEL.INTERMEDIATE, name: "Intermediate", /*tournaments: [],*/ total_players: 0,
                            buy_in: sngConsts.TOURNAMENT_FEES_5.INTERMEDIATE.BUY_IN,
                            fee: sngConsts.TOURNAMENT_FEES_5.INTERMEDIATE.FEE,
                            reward_distribution: {
                                first_place: (sngConsts.TOURNAMENT_FEES_5.INTERMEDIATE.BUY_IN * sng5Value)*(firstPlace/100),
                                second_place: (sngConsts.TOURNAMENT_FEES_5.INTERMEDIATE.BUY_IN * sng5Value)*(secondPlace/100),
                                third_place: (sngConsts.TOURNAMENT_FEES_5.INTERMEDIATE.BUY_IN * sng5Value)*(thirdPlace/100)
                            }
                        },
                        {
                            level: sngConsts.TOURNAMENT_LEVEL.ADVANCED, name: "Advanced", /*tournaments: [],*/ total_players: 0,
                            buy_in: sngConsts.TOURNAMENT_FEES_5.ADVANCED.BUY_IN,
                            fee: sngConsts.TOURNAMENT_FEES_5.ADVANCED.FEE,
                            reward_distribution: {
                                first_place: (sngConsts.TOURNAMENT_FEES_5.ADVANCED.BUY_IN * sng5Value)*(firstPlace/100),
                                second_place: (sngConsts.TOURNAMENT_FEES_5.ADVANCED.BUY_IN * sng5Value)*(secondPlace/100),
                                third_place: (sngConsts.TOURNAMENT_FEES_5.ADVANCED.BUY_IN * sng5Value)*(thirdPlace/100)
                            }
                        },
                        {
                            level: sngConsts.TOURNAMENT_LEVEL.PRO, name: "Pro", /*tournaments: [],*/ total_players: 0,
                            buy_in: sngConsts.TOURNAMENT_FEES_5.PRO.BUY_IN,
                            fee: sngConsts.TOURNAMENT_FEES_5.PRO.FEE,
                            reward_distribution: {
                                first_place: (sngConsts.TOURNAMENT_FEES_5.PRO.BUY_IN * sng5Value)*(firstPlace/100),
                                second_place: (sngConsts.TOURNAMENT_FEES_5.PRO.BUY_IN * sng5Value)*(secondPlace/100),
                                third_place: (sngConsts.TOURNAMENT_FEES_5.PRO.BUY_IN * sng5Value)*(thirdPlace/100)
                            }
                        }
                    ]
                },
                {
                    type: sngConsts.TOURNAMENT_TYPE.SNG_9,
                    name: "9 Players",
                    levels: [
                        {
                            level: sngConsts.TOURNAMENT_LEVEL.BEGINNER, name: "Beginner", /*tournaments: [],*/ total_players: 0,
                            buy_in: sngConsts.TOURNAMENT_FEES_9.BEGINNER.BUY_IN,
                            fee: sngConsts.TOURNAMENT_FEES_9.BEGINNER.FEE,
                            reward_distribution: {
                                first_place: (sngConsts.TOURNAMENT_FEES_5.PRO.BUY_IN * sng9Value)*(firstPlace/100),
                                second_place: (sngConsts.TOURNAMENT_FEES_5.PRO.BUY_IN * sng9Value)*(secondPlace/100),
                                third_place: (sngConsts.TOURNAMENT_FEES_5.PRO.BUY_IN * sng9Value)*(thirdPlace/100)
                            }
                        },
                        {
                            level: sngConsts.TOURNAMENT_LEVEL.INTERMEDIATE, name: "Intermediate", /*tournaments: [],*/ total_players: 0,
                            buy_in: sngConsts.TOURNAMENT_FEES_9.INTERMEDIATE.BUY_IN,
                            fee: sngConsts.TOURNAMENT_FEES_9.INTERMEDIATE.FEE,
                            reward_distribution: {
                                first_place: (sngConsts.TOURNAMENT_FEES_5.PRO.BUY_IN * sng9Value)*(firstPlace/100),
                                second_place: (sngConsts.TOURNAMENT_FEES_5.PRO.BUY_IN * sng9Value)*(secondPlace/100),
                                third_place: (sngConsts.TOURNAMENT_FEES_5.PRO.BUY_IN * sng9Value)*(thirdPlace/100)
                            }
                        },
                        {
                            level: sngConsts.TOURNAMENT_LEVEL.ADVANCED, name: "Advanced", /*tournaments: [],*/ total_players: 0,
                            buy_in: sngConsts.TOURNAMENT_FEES_9.ADVANCED.BUY_IN,
                            fee: sngConsts.TOURNAMENT_FEES_9.ADVANCED.FEE,
                            reward_distribution: {
                                first_place: (sngConsts.TOURNAMENT_FEES_5.PRO.BUY_IN * sng9Value)*(firstPlace/100),
                                second_place: (sngConsts.TOURNAMENT_FEES_5.PRO.BUY_IN * sng9Value)*(secondPlace/100),
                                third_place: (sngConsts.TOURNAMENT_FEES_5.PRO.BUY_IN * sng9Value)*(thirdPlace/100)
                            }
                        },
                        {
                            level: sngConsts.TOURNAMENT_LEVEL.PRO, name: "Pro", /*tournaments: [],*/ total_players: 0,
                            buy_in: sngConsts.TOURNAMENT_FEES_9.PRO.BUY_IN,
                            fee: sngConsts.TOURNAMENT_FEES_9.PRO.FEE,
                            reward_distribution: {
                                first_place: (sngConsts.TOURNAMENT_FEES_5.PRO.BUY_IN * sng9Value)*(firstPlace/100),
                                second_place: (sngConsts.TOURNAMENT_FEES_5.PRO.BUY_IN * sng9Value)*(secondPlace/100),
                                third_place: (sngConsts.TOURNAMENT_FEES_5.PRO.BUY_IN * sng9Value)*(thirdPlace/100)
                            }
                        }
                    ]
                }
            ]
        };

        if (tournaments && tournaments.length > 0) {
            tournaments.forEach(function(tournament) {
                var dto = new SngTournamentDTO({
                    id: tournament.id,
                    code: tournament.code,
                    status: tournament.status,
                    player_capacity: tournament.player_capacity,
                    buy_in: tournament.buy_in,
                    fee: tournament.fee,
                    reward_pool: tournament.reward_pool,
                    created_at: tournament.created_at,
                    started_at: tournament.started_at,
                    ended_at: tournament.ended_at,
                    registered_players: tournament.players ? tournament.players.length : 0,
                    blind_level: 1,
                    current_small_blind: tournament.blindLevels && tournament.blindLevels[0] ? tournament.blindLevels[0].small_blind : sngConsts.DEFAULT_BLIND_STRUCTURE[0].smallBlind,
                    current_big_blind: tournament.blindLevels && tournament.blindLevels[0] ? tournament.blindLevels[0].big_blind : sngConsts.DEFAULT_BLIND_STRUCTURE[0].bigBlind,
                    current_ante: tournament.blindLevels && tournament.blindLevels[0] ? tournament.blindLevels[0].ante : sngConsts.DEFAULT_BLIND_STRUCTURE[0].ante
                });

                // Determine tournament type and level
                var tournamentType = tournament.player_capacity === 5 ? sngConsts.TOURNAMENT_TYPE.SNG_5 : sngConsts.TOURNAMENT_TYPE.SNG_9;
                var tournamentLevel;

                // Determine level based on buy_in
                switch (tournament.buy_in) {
                    case 50000000: // 50M
                        tournamentLevel = sngConsts.TOURNAMENT_LEVEL.BEGINNER;
                        break;
                    case 100000000: // 100M
                        tournamentLevel = sngConsts.TOURNAMENT_LEVEL.INTERMEDIATE;
                        break;
                    case 200000000: // 200M
                        tournamentLevel = sngConsts.TOURNAMENT_LEVEL.ADVANCED;
                        break;
                    case 500000000: // 500M
                        tournamentLevel = sngConsts.TOURNAMENT_LEVEL.PRO;
                        break;
                    default:
                        tournamentLevel = sngConsts.TOURNAMENT_LEVEL.BEGINNER;
                }

                // Add tournament to the appropriate category
                var typeIndex = tournamentType === sngConsts.TOURNAMENT_TYPE.SNG_5 ? 0 : 1;
                var levelIndex;

                switch (tournamentLevel) {
                    case sngConsts.TOURNAMENT_LEVEL.BEGINNER:
                        levelIndex = 0;
                        break;
                    case sngConsts.TOURNAMENT_LEVEL.INTERMEDIATE:
                        levelIndex = 1;
                        break;
                    case sngConsts.TOURNAMENT_LEVEL.ADVANCED:
                        levelIndex = 2;
                        break;
                    case sngConsts.TOURNAMENT_LEVEL.PRO:
                        levelIndex = 3;
                        break;
                    default:
                        levelIndex = 0;
                }

                // Add tournament to the appropriate category (không cần trả về danh sách các giải đấu đang mở)
                // organizedTournaments.types[typeIndex].levels[levelIndex].tournaments.push(dto);

                // Update total players count for this type and level
                organizedTournaments.types[typeIndex].levels[levelIndex].total_players += (tournament.players ? tournament.players.length : 0);

                // Also keep the flat list for backward compatibility
                tournamentDTOs.push(dto);
            });
        }

        if (cb) cb(null, organizedTournaments);
    }).catch(function(err) {
        logger.error('Error getting tournaments:', err);
        if (cb) cb(err);
    });
};

/**
 * Get tournament by ID
 */
SngTournamentService.prototype.getTournamentById = function(tournamentId, cb) {
    var self = this;

    models.SngTournament.findByPk(tournamentId, {
        include: [
            {
                model: models.SngTournamentPlayer,
                as: 'players',
                include: [
                    {
                        model: models.Player,
                        as: 'player'
                    }
                ]
            },
            {
                model: models.SngBlindLevel,
                as: 'blindLevels'
            }
        ]
    }).then(function(tournament) {
        if (!tournament) {
            return cb(CODE.SNG.TOURNAMENT_NOT_FOUND);
        }

        var playerDTOs = [];
        if (tournament.players && tournament.players.length > 0) {
            tournament.players.forEach(function(player) {
                playerDTOs.push(new SngTournamentPlayerDTO({
                    id: player.id,
                    tournament_id: player.tournament_id,
                    player_id: player.player_id,
                    seat_number: player.seat_number,
                    initial_chips: player.initial_chips,
                    current_chips: player.current_chips,
                    status: player.status,
                    eliminated_at_hand: player.eliminated_at_hand,
                    rank: player.rank,
                    joined_at: player.joined_at,
                    player_name: player.player ? player.player.nick_name : '',
                    avatar: player.player ? player.player.avatar : '',
                    level: player.player ? player.player.level : 0,
                    vippoint: player.player ? player.player.vip_point : 0,
                    exp: player.player ? player.player.exp : 0
                }));
            });
        }

        var blindLevelDTOs = [];
        if (tournament.blindLevels && tournament.blindLevels.length > 0) {
            tournament.blindLevels.forEach(function(level) {
                blindLevelDTOs.push(new SngBlindLevelDTO({
                    id: level.id,
                    tournament_id: level.tournament_id,
                    level_number: level.level_number,
                    small_blind: level.small_blind,
                    big_blind: level.big_blind,
                    ante: level.ante,
                    duration_seconds: level.duration_seconds
                }));
            });
        }

        var dto = new SngTournamentDTO({
            id: tournament.id,
            code: tournament.code,
            status: tournament.status,
            player_capacity: tournament.player_capacity,
            buy_in: tournament.buy_in,
            fee: tournament.fee,
            reward_pool: tournament.reward_pool,
            created_at: tournament.created_at,
            started_at: tournament.started_at,
            ended_at: tournament.ended_at,
            registered_players: playerDTOs.length,
            blind_level: self.getCurrentBlindLevel(tournament.id)
        });

        // Add current blind level info
        var currentLevel = self.getCurrentBlindLevelInfo(tournament.id, blindLevelDTOs);
        if (currentLevel) {
            dto.current_small_blind = currentLevel.small_blind;
            dto.current_big_blind = currentLevel.big_blind;
            dto.current_ante = currentLevel.ante;
        }

        cb(null, {
            tournament: dto,
            players: playerDTOs,
            blindLevels: blindLevelDTOs
        });
    }).catch(function(err) {
        logger.error('Error getting tournament by ID:', err);
        cb(err);
    });
};

/**
 * Get current blind level for a tournament
 */
SngTournamentService.prototype.getCurrentBlindLevel = function(tournamentId) {
    var self = this;

    // Check if we have the tournament in memory
    if (self.tournaments[tournamentId] && self.tournaments[tournamentId].currentBlindLevel) {
        return self.tournaments[tournamentId].currentBlindLevel;
    }

    // If not in memory, default to level 1
    return 1;
};

/**
 * Get current blind level info for a tournament
 */
SngTournamentService.prototype.getCurrentBlindLevelInfo = function(tournamentId, blindLevels) {
    var currentLevel = this.getCurrentBlindLevel(tournamentId);

    if (blindLevels && blindLevels.length > 0) {
        var levelInfo = _.find(blindLevels, function(level) {
            return level.level_number === currentLevel;
        });

        if (levelInfo) {
            return levelInfo;
        }
    }

    // Default to the first level in the default structure
    return sngConsts.DEFAULT_BLIND_STRUCTURE[currentLevel - 1];
};

/**
 * Create a new SNG tournament
 */
SngTournamentService.prototype.createTournament = function(options, cb) {
    var self = this;

    // Generate tournament code
    var code = 'SNG_' + moment().format('YYYYMMDD') + '_' + Math.floor(Math.random() * 1000).toString().padStart(3, '0');

    // Find tournament configuration
    var tournamentConfig = null;
    var tournamentType = options.type || '5_PLAYERS';
    var tournamentLevel = options.level || 'BEGINNER';

    // Look for matching tournament configuration
    if (this.tournamentConfigs && this.tournamentConfigs.tournaments) {
        tournamentConfig = this.tournamentConfigs.tournaments.find(function(config) {
            return (config.type === tournamentType || config.player_capacity === parseInt(options.player_capacity)) &&
                   config.level === tournamentLevel;
        });
    }

    logger.info('Creating tournament with options:', options);
    logger.info('Found tournament config:', tournamentConfig);

    // Use provided parameters or configuration values or defaults
    var playerCapacity = options.player_capacity || (tournamentConfig ? tournamentConfig.player_capacity : 5);
    var buyIn = options.buy_in || (tournamentConfig ? tournamentConfig.buy_in : 50000000);
    var fee = options.fee || (tournamentConfig ? tournamentConfig.fee : 5000000);
    var initialChips = options.initial_chips || (tournamentConfig ? tournamentConfig.initial_chips : 100000000);
    var name = options.name || (tournamentConfig ? tournamentConfig.name : 'SNG Tournament');

    logger.info('Creating tournament with configuration:', {
        type: tournamentType,
        level: tournamentLevel,
        player_capacity: playerCapacity,
        buy_in: buyIn,
        fee: fee,
        initial_chips: initialChips,
        name: name
    });

    // Use a transaction to ensure all operations are performed atomically
    sequelize.transaction().then(function(t) {
        // Create tournament record
        return models.SngTournament.create({
            code: code,
            name: name,
            status: 'WAITING',
            player_capacity: playerCapacity,
            buy_in: buyIn,
            fee: fee,
            initial_chips: initialChips,
            reward_pool: 0, // Will be updated as players register
            created_at: new Date()
        }, { transaction: t }).then(function(tournament) {
            // Create blind levels for the tournament
            var blindLevelPromises = [];

            // Use blind structure from configuration or default
            var blindStructure = self.tournamentConfigs.blind_structure || sngConsts.DEFAULT_BLIND_STRUCTURE;

            blindStructure.forEach(function(level, index) {
                // Get duration from level or default
                var durationSeconds = level.duration_seconds ||
                    (tournamentConfig ? tournamentConfig.blind_duration_minutes * 60 : 300);

                blindLevelPromises.push(
                    models.SngBlindLevel.create({
                        tournament_id: tournament.id,
                        level_number: index + 1,
                        small_blind: level.small_blind || level.smallBlind,
                        big_blind: level.big_blind || level.bigBlind,
                        ante: level.ante,
                        duration_seconds: durationSeconds
                    }, { transaction: t })
                );
            });

            return Promise.all(blindLevelPromises).then(function() {
                // Add tournament to memory
                self.tournaments[tournament.id] = {
                    currentBlindLevel: 1,
                    config: tournamentConfig
                };

                // Commit transaction
                return t.commit().then(function() {
                    logger.info('Tournament created successfully:', tournament.id);
                    if (cb) cb(null, tournament);
                    return tournament;
                });
            });
        }).catch(function(err) {
            // Rollback transaction on error
            t.rollback();
            logger.error('Error creating tournament:', err, ' -> message:', err.message, ' -> stack:', err.stack);
            if (cb) cb(err);
            throw err;
        });
    }).catch(function(err) {
        logger.error('Error starting transaction:', err, ' -> message:', err.message, ' -> stack:', err.stack);
        if (cb) cb(err);
    });
};

/**
 * Register a player for a tournament
 */
SngTournamentService.prototype.registerPlayer = function(tournamentId, playerId, cb) {
    var self = this;

    // Check if tournament exists
    models.SngTournament.findByPk(tournamentId).then(function(tournament) {
        if (!tournament) {
            return cb(CODE.SNG.TOURNAMENT_NOT_FOUND);
        }

        // Check if tournament is in a valid state for registration
        if (tournament.status !== 'WAITING') {
            return cb(CODE.SNG.TOURNAMENT_ALREADY_STARTED);
        }

        // Check if player is already registered for this tournament
        models.SngTournamentPlayer.findOne({
            where: {
                tournament_id: tournamentId,
                player_id: playerId
            }
        }).then(function(existingRegistration) {
            if (existingRegistration) {
                return cb(CODE.SNG.PLAYER_ALREADY_REGISTERED);
            }

            // Check if player is already registered for another active tournament
            models.SngTournamentPlayer.findOne({
                include: [
                    {
                        model: models.SngTournament,
                        as: 'tournament',
                        where: {
                            status: {
                                // [models.Sequelize.Op.ne]: 'ENDED'
                                [Op.ne]: 'ENDED'
                            }
                        }
                    }
                ],
                where: {
                    player_id: playerId
                }
            }).then(function(otherRegistration) {
                if (otherRegistration) {
                    return cb(CODE.SNG.TOURNAMENT_ALREADY_REGISTERED);
                }

                // Check if player has enough chips
                models.Player.findByPk(playerId).then(function(player) {
                    logger.info('[sngTournamentService.registerPlayer] Player balance:', player.balance, ' -> tournament buy-in:', tournament.buy_in, ' -> tournament fee:', tournament.fee, ' -> player: ', player);
                    const currentBalance = player?.balance || 0;
                    if (!player) {
                        return cb(CODE.TABLE.USER_NOT_FOUND);
                    }

                    if (player.balance < (tournament.buy_in + tournament.fee)) {
                        return cb(CODE.SNG.NOT_ENOUGH_CHIPS);
                    }

                    // Deduct buy-in and fee from player's balance
                    player.balance -= (tournament.buy_in + tournament.fee);
                    player.save().then(function() {
                        // Add to reward pool
                        tournament.reward_pool += tournament.buy_in;
                        tournament.save().then(function() {
                            // Get initial chips from tournament or default
                            var initialChips = tournament.initial_chips || sngConsts.DEFAULT_SETTINGS.INITIAL_CHIPS;

                            // Register player for tournament
                            models.SngTournamentPlayer.create({
                                tournament_id: tournamentId,
                                player_id: playerId,
                                initial_chips: initialChips,
                                current_chips: initialChips,
                                status: 'ACTIVE',
                                joined_at: new Date()
                            }).then(function(registration) {
                                // Add to player-tournament mapping
                                self.playerTournaments[playerId] = tournamentId;

                                // Log the registration
                                self.logTournamentAction(playerId, tournamentId, logBoards.SNG_REGISTER, {
                                    buy_in: tournament.buy_in,
                                    fee: tournament.fee,
                                    initial_chips: sngConsts.DEFAULT_SETTINGS.INITIAL_CHIPS
                                });

                                // Notify other players about the new player joining
                                self.notifyTournamentJoin(tournamentId, playerId);

                                // Add log into table: transactions
                                models.Transactions.create({
                                    player_id: playerId,
                                    amount: -(tournament.buy_in + tournament.fee),
                                    before_balance: currentBalance, // player.balance,
                                    after_balance: player.balance, // player.balance - (tournament.buy_in + tournament.fee),
                                    type: 'SNG_TOURNAMENT',
                                    action: 'SNG_REGISTER',
                                    reference_id: tournamentId,
                                    reference_type: 'SNG_TOURNAMENT',
                                    meta: {
                                        tournament_id: tournamentId,
                                        buy_in: tournament.buy_in,
                                        fee: tournament.fee,
                                        initial_chips: sngConsts.DEFAULT_SETTINGS.INITIAL_CHIPS
                                    },
                                    description: 'Register for SNG Tournament',
                                });

                                // Check if tournament is now full and should be set to READY
                                models.SngTournamentPlayer.count({
                                    where: {
                                        tournament_id: tournamentId
                                    }
                                }).then(function(playerCount) {
                                    if (playerCount >= tournament.player_capacity) {
                                        tournament.status = 'READY';
                                        tournament.save().then(function() {
                                            // Start the tournament
                                            self.startTournament(tournamentId);

                                            // Create a new tournament of the same type to replace this one
                                            self.createSimilarTournament(tournament);

                                            if (cb) cb(null, registration);
                                        }).catch(function(err) {
                                            logger.error('Error updating tournament status:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
                                            if (cb) cb(err);
                                        });
                                    } else {
                                        if (cb) cb(null, registration);
                                    }
                                }).catch(function(err) {
                                    logger.error('Error counting players:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
                                    if (cb) cb(err);
                                });
                            }).catch(function(err) {
                                logger.error('Error registering player for tournament:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
                                if (cb) cb(err);
                            });
                        }).catch(function(err) {
                            logger.error('Error updating tournament reward pool:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
                            if (cb) cb(err);
                        });
                    }).catch(function(err) {
                        logger.error('Error updating player balance:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
                        if (cb) cb(err);
                    });
                }).catch(function(err) {
                    logger.error('Error finding player:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
                    if (cb) cb(err);
                });
            }).catch(function(err) {
                logger.error('Error checking other tournament registrations:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
                if (cb) cb(err);
            });
        }).catch(function(err) {
            logger.error('Error checking existing registration:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
            if (cb) cb(err);
        });
    }).catch(function(err) {
        logger.error('Error finding tournament:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
        if (cb) cb(err);
    });
};

/**
 * Log a tournament action
 */
SngTournamentService.prototype.logTournamentAction = function(playerId, tournamentId, actionType, data) {
    // Add tournament ID to data
    var logData = Object.assign({}, data || {}, { tournament_id: tournamentId });
    var timestamp = Math.floor(Date.now() / 1000);
    var amount = -(data.buy_in + data.fee) || 0;

    // Log to general logs table
    /*
    models.Logs.create({
        type: actionType,
        player_id: playerId,
        // amount: data.amount || 0,
        amount: amount,
        data_raw: logData,
        log_timestamp: timestamp
    }).catch(function(err) {
        logger.error('Error logging tournament action to Logs:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
    });
    */

    // Log to SNG tournament logs table
    models.SngTournamentLog.create({
        tournament_id: tournamentId,
        player_id: playerId,
        action_type: actionType,
        data: logData,
        amount: amount,
        created_at: new Date()
    }).catch(function(err) {
        logger.error('Error logging tournament action to SngTournamentLog:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
    });

    // Log to console for debugging
    logger.debug('Tournament action logged:', {
        type: actionType,
        player_id: playerId,
        tournament_id: tournamentId,
        data: logData
    });
};

/**
 * Start a tournament
 */
SngTournamentService.prototype.startTournament = function(tournamentId) {
    var self = this;

    // Get tournament details
    models.SngTournament.findByPk(tournamentId, {
        include: [
            {
                model: models.SngTournamentPlayer,
                as: 'players',
                include: [
                    {
                        model: models.Player,
                        as: 'player'
                    }
                ]
            },
            {
                model: models.SngBlindLevel,
                as: 'blindLevels',
                where: {
                    level_number: 1
                },
                required: false
            }
        ]
    }).then(function(tournament) {
        if (!tournament) {
            logger.error('Tournament not found:', tournamentId);
            return;
        }

        if (tournament.status !== 'READY') {
            logger.error('Tournament not in READY state:', tournamentId);
            return;
        }

        // Update tournament status
        tournament.status = 'IN_PROGRESS';
        tournament.started_at = new Date();
        tournament.save().then(function() {
            // Create tournament table
            self.createTournamentTable(tournament);

            // Start blind timer
            self.startBlindTimer(tournamentId);

            // Notify all players that the tournament has started
            self.notifyTournamentStatus(tournament);
        }).catch(function(err) {
            logger.error('Error updating tournament status:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
        });
    }).catch(function(err) {
        logger.error('Error finding tournament:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
    });
};

/**
 * Create a tournament table
 */
SngTournamentService.prototype.createTournamentTable = function(tournament) {
    var self = this;

    // Create a new table for the tournament
    var tableId = uuid.v1();
    var blindLevel = tournament.blindLevels && tournament.blindLevels[0] ? tournament.blindLevels[0] : sngConsts.DEFAULT_BLIND_STRUCTURE[0];

    // Create table object
    var tableObj = {
        id: tableId,
        creator: tournament.players[0].player_id, // Use first player as creator
        state: 'READY',
        tableService: self,
        display_tid: tournament.id, // Use tournament ID as display ID
        tournament_id: tournament.id // Link to tournament
    };

    // Create table instance
    var smallBlind = blindLevel.small_blind || blindLevel.smallBlind;
    var bigBlind = blindLevel.big_blind || blindLevel.bigBlind;

    tableObj.table = new Table(
        smallBlind,
        bigBlind,
        2, // minPlayers
        tournament.player_capacity, // maxPlayers
        sngConsts.DEFAULT_SETTINGS.INITIAL_CHIPS, // minBuyIn
        sngConsts.DEFAULT_SETTINGS.INITIAL_CHIPS, // maxBuyIn
        consts.GAME.MODE.SNG, // gameMode
        tableObj
    );

    // Store table in service
    self.tournamentTables[tournament.id] = tableObj;

    // Add players to table
    tournament.players.forEach(function(player, index) {
        // Assign random seats
        var seatNumber = index;

        // Update player's seat number
        player.seat_number = seatNumber;
        player.save().catch(function(err) {
            logger.error('Error updating player seat number:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
        });

        // Add player to table
        tableObj.table.AddPlayer(
            seatNumber,
            player.player.nick_name,
            player.current_chips,
            player.player_id,
            player.player.avatar,
            player.player.level,
            player.player.vip_point,
            player.player.exp,
            player.player.type
        );
    });

    // Start the game
    tableObj.table.StartGame();

    // Return the table
    return tableObj;
};

/**
 * Start blind timer for a tournament
 */
SngTournamentService.prototype.startBlindTimer = function(tournamentId) {
    var self = this;

    // Clear existing timer if any
    if (self.blindTimers[tournamentId]) {
        clearTimeout(self.blindTimers[tournamentId]);
        logger.info('Cleared existing blind timer for tournament:', tournamentId);
    }

    // Get tournament to check status
    models.SngTournament.findByPk(tournamentId).then(function(tournament) {
        if (!tournament || tournament.status !== 'IN_PROGRESS') {
            logger.info('Tournament not in progress, not starting blind timer:', tournamentId);
            return;
        }

        // Get current blind level
        var currentLevel = self.getCurrentBlindLevel(tournamentId);

        // Get blind level duration
        models.SngBlindLevel.findOne({
            where: {
                tournament_id: tournamentId,
                level_number: currentLevel
            }
        }).then(function(blindLevel) {
            // Use configured duration or default
            var durationSeconds = blindLevel && blindLevel.duration_seconds ?
                blindLevel.duration_seconds :
                sngConsts.DEFAULT_SETTINGS.BLIND_INCREASE_MINUTES * 60;

            logger.info('Starting blind timer for tournament:', {
                tournament_id: tournamentId,
                current_level: currentLevel,
                duration_seconds: durationSeconds
            });

            // Set timer for next blind increase
            self.blindTimers[tournamentId] = setTimeout(function() {
                self.increaseBlindLevel(tournamentId);
            }, durationSeconds * 1000);
        }).catch(function(err) {
            logger.error('Error getting blind level duration:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);

            // Use default duration as fallback
            logger.info('Using default blind duration for tournament:', tournamentId);

            self.blindTimers[tournamentId] = setTimeout(function() {
                self.increaseBlindLevel(tournamentId);
            }, sngConsts.DEFAULT_SETTINGS.BLIND_INCREASE_MINUTES * 60 * 1000);
        });
    }).catch(function(err) {
        logger.error('Error checking tournament status for blind timer:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
    });
};

/**
 * Increase blind level for a tournament
 */
SngTournamentService.prototype.increaseBlindLevel = function(tournamentId) {
    var self = this;

    logger.info('Increasing blind level for tournament:', tournamentId);

    // Get tournament
    models.SngTournament.findByPk(tournamentId, {
        include: [
            {
                model: models.SngBlindLevel,
                as: 'blindLevels'
            },
            {
                model: models.SngTournamentPlayer,
                as: 'players',
                attributes: ['id', 'player_id', 'status']
            }
        ]
    }).then(function(tournament) {
        if (!tournament) {
            logger.error('Tournament not found:', tournamentId);
            return;
        }

        if (tournament.status !== 'IN_PROGRESS') {
            logger.info('Tournament not in progress, not increasing blind level:', tournamentId);
            return;
        }

        // Check if there are enough active players to continue
        var activePlayers = _.filter(tournament.players, function(player) {
            return player.status === 'ACTIVE';
        });

        if (activePlayers.length <= 1) {
            logger.info('Not enough active players to increase blind level:', {
                tournament_id: tournamentId,
                active_players: activePlayers.length
            });
            return;
        }

        // Get current blind level
        var currentLevel = self.getCurrentBlindLevel(tournamentId);
        var nextLevel = currentLevel + 1;

        logger.info('Current blind level:', currentLevel, 'Next blind level:', nextLevel);

        // Find next blind level in database
        models.SngBlindLevel.findOne({
            where: {
                tournament_id: tournamentId,
                level_number: nextLevel
            }
        }).then(function(nextBlindLevel) {
            // If not found in database, check default structure
            if (!nextBlindLevel && nextLevel <= sngConsts.DEFAULT_BLIND_STRUCTURE.length) {
                // Convert default structure to match database model format
                var defaultLevel = sngConsts.DEFAULT_BLIND_STRUCTURE[nextLevel - 1];
                nextBlindLevel = {
                    level_number: nextLevel,
                    small_blind: defaultLevel.smallBlind || defaultLevel.small_blind || defaultLevel.level,
                    big_blind: defaultLevel.bigBlind || defaultLevel.big_blind || (defaultLevel.level * 2),
                    ante: defaultLevel.ante || 0,
                    duration_seconds: sngConsts.DEFAULT_SETTINGS.BLIND_INCREASE_MINUTES * 60
                };

                logger.info('Using default blind level structure:', nextBlindLevel);
            }

            if (nextBlindLevel) {
                // Store the current level in memory for this tournament
                self.tournaments[tournamentId] = self.tournaments[tournamentId] || {};
                self.tournaments[tournamentId].currentBlindLevel = nextLevel;

                // Update table blinds
                var tableObj = self.tournamentTables[tournamentId];
                if (tableObj && tableObj.table) {
                    var smallBlind = nextBlindLevel.small_blind || nextBlindLevel.smallBlind;
                    var bigBlind = nextBlindLevel.big_blind || nextBlindLevel.bigBlind;
                    var ante = nextBlindLevel.ante || 0;

                    logger.info('Updating table blinds:', {
                        tournament_id: tournamentId,
                        small_blind: smallBlind,
                        big_blind: bigBlind,
                        ante: ante
                    });

                    tableObj.table.smallBlind = smallBlind;
                    tableObj.table.bigBlind = bigBlind;
                    tableObj.table.ante = ante;
                } else {
                    logger.warn('Table not found for tournament:', tournamentId);
                }

                // Log blind increase
                self.logTournamentAction(null, tournamentId, logBoards.SNG_BLIND_INCREASE, {
                    level: nextLevel,
                    small_blind: nextBlindLevel.small_blind || nextBlindLevel.smallBlind,
                    big_blind: nextBlindLevel.big_blind || nextBlindLevel.bigBlind,
                    ante: nextBlindLevel.ante || 0,
                    active_players: activePlayers.length
                });

                // Notify players of blind increase
                self.notifyBlindIncrease(tournamentId, nextLevel, nextBlindLevel);

                // Start timer for next level
                self.startBlindTimer(tournamentId);

                logger.info('Blind level increased successfully:', {
                    tournament_id: tournamentId,
                    new_level: nextLevel,
                    small_blind: nextBlindLevel.small_blind || nextBlindLevel.smallBlind,
                    big_blind: nextBlindLevel.big_blind || nextBlindLevel.bigBlind,
                    ante: nextBlindLevel.ante || 0
                });
            } else {
                logger.warn('No more blind levels available for tournament:', tournamentId);

                // If we've reached the maximum blind level, just restart the timer with the last level
                self.startBlindTimer(tournamentId);
            }
        }).catch(function(err) {
            logger.error('Error finding next blind level:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);

            // Restart timer as fallback
            self.startBlindTimer(tournamentId);
        });
    }).catch(function(err) {
        logger.error('Error increasing blind level:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);

        // Restart timer as fallback
        self.startBlindTimer(tournamentId);
    });
};

/**
 * Helper function to get receivers from channel for pushMessageByUids
 * @param {Object} channel - Channel object
 * @returns {Array} Array of receivers with uid and sid
 */
SngTournamentService.prototype.getChannelReceivers = function(channel) {
    var receivers = [];
    if (channel) {
        var members = channel.getMembers();
        for (var memberId in members) {
            var member = members[memberId];
            if (member && member.uid && member.sid) {
                receivers.push({
                    uid: member.uid,
                    sid: member.sid
                });
            }
        }
    }
    return receivers;
};

/**
 * Helper function to send push message using pushMessageByUids format
 * @param {Object} channelService - Channel service
 * @param {String} channelName - Channel name
 * @param {Object} message - Message to send
 * @param {Function} callback - Callback function
 */
SngTournamentService.prototype.sendPushMessage = function(channelService, channelName, message, callback) {
    if (!channelService) {
        logger.warn('Channel service not available for push message:', {
            channel: channelName,
            route: message.route
        });
        if (callback) callback(new Error('Channel service not available'));
        return;
    }

    var channel = channelService.getChannel(channelName, true);
    if (!channel) {
        logger.warn('Channel not found for push message:', {
            channel: channelName,
            route: message.route
        });
        if (callback) callback(new Error('Channel not found'));
        return;
    }

    var receivers = this.getChannelReceivers(channel);
    if (receivers.length === 0) {
        logger.warn('No receivers found for push message:', {
            channel: channelName,
            route: message.route
        });
        if (callback) callback(new Error('No receivers found'));
        return;
    }

    channelService.pushMessageByUids(message, receivers, function(err) {
        if (err) {
            logger.error('Error sending push message:', err, {
                channel: channelName,
                route: message.route,
                receivers_count: receivers.length
            });
        } else {
            logger.info('Push message sent successfully:', {
                channel: channelName,
                route: message.route,
                receivers_count: receivers.length
            });
        }
        if (callback) callback(err);
    });
};

/**
 * Notify players when a new player joins the tournament
 */
SngTournamentService.prototype.notifyTournamentJoin = function(tournamentId, playerId) {
    var self = this;
    var channelService = self.app.get('channelService');
    logger.info('[sngTournamentService.notifyTournamentJoin] channelService: ', channelService);
    // Get tournament details
    models.SngTournament.findByPk(tournamentId, {
        include: [
            {
                model: models.SngTournamentPlayer,
                as: 'players',
                include: [
                    {
                        model: models.Player,
                        as: 'player'
                    }
                ]
            }
        ]
    }).then(function(tournament) {
        if (!tournament) {
            logger.error('Tournament not found for join notification:', tournamentId);
            return;
        }

        // Find the player who joined
        var joinedPlayer = _.find(tournament.players, function(player) {
            return player.player_id === playerId;
        });

        if (!joinedPlayer) {
            logger.error('Joined player not found in tournament:', playerId);
            return;
        }

        // Create player DTO
        var playerDTO = new SngTournamentPlayerDTO({
            id: joinedPlayer.id,
            tournament_id: joinedPlayer.tournament_id,
            player_id: joinedPlayer.player_id,
            player_name: joinedPlayer.player.nick_name,
            seat_number: joinedPlayer.seat_number,
            initial_chips: joinedPlayer.initial_chips,
            current_chips: joinedPlayer.current_chips,
            status: joinedPlayer.status,
            joined_at: joinedPlayer.joined_at,
            avatar: joinedPlayer.player.avatar,
            level: joinedPlayer.player.level,
            vippoint: joinedPlayer.player.vip_point,
            exp: joinedPlayer.player.exp
        });

        // Create tournament DTO
        var tournamentDTO = new SngTournamentDTO({
            id: tournament.id,
            code: tournament.code,
            status: tournament.status,
            player_capacity: tournament.player_capacity,
            buy_in: tournament.buy_in,
            fee: tournament.fee,
            reward_pool: tournament.reward_pool,
            created_at: tournament.created_at,
            registered_players: tournament.players ? tournament.players.length : 0
        });

        // Send notification to all players in the tournament channel
        self.sendPushMessage(channelService, tournamentId.toString(), {
            route: consts.GAME.ROUTER.SNG_TOURNAMENT_JOIN,
            tournament: tournamentDTO,
            player: playerDTO
        }, function(err) {
            if (!err) {
                logger.info('SNG Tournament join notification sent:', {
                    tournament_id: tournamentId,
                    player_id: playerId,
                    registered_players: tournament.players.length
                });
            }
        });
    }).catch(function(err) {
        logger.error('Error sending tournament join notification:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
    });
};

/**
 * Notify players of tournament status change
 */
SngTournamentService.prototype.notifyTournamentStatus = function(tournament) {
    var self = this;
    var channelService = self.app.get('channelService');
    logger.info('[sngTournamentService.notifyTournamentStatus] channelService: ', channelService);
    // Create tournament DTO
    var dto = new SngTournamentDTO({
        id: tournament.id,
        code: tournament.code,
        status: tournament.status,
        player_capacity: tournament.player_capacity,
        buy_in: tournament.buy_in,
        fee: tournament.fee,
        reward_pool: tournament.reward_pool,
        created_at: tournament.created_at,
        started_at: tournament.started_at,
        ended_at: tournament.ended_at,
        registered_players: tournament.players ? tournament.players.length : 0,
        blind_level: self.getCurrentBlindLevel(tournament.id)
    });

    // Add current blind level info
    var currentLevel = self.getCurrentBlindLevelInfo(tournament.id, tournament.blindLevels);
    if (currentLevel) {
        dto.current_small_blind = currentLevel.small_blind || currentLevel.smallBlind;
        dto.current_big_blind = currentLevel.big_blind || currentLevel.bigBlind;
        dto.current_ante = currentLevel.ante;
    }

    // Create player DTOs
    var playerDTOs = [];
    if (tournament.players && tournament.players.length > 0) {
        tournament.players.forEach(function(player) {
            playerDTOs.push(new SngTournamentPlayerDTO({
                id: player.id,
                tournament_id: player.tournament_id,
                player_id: player.player_id,
                seat_number: player.seat_number,
                initial_chips: player.initial_chips,
                current_chips: player.current_chips,
                status: player.status,
                eliminated_at_hand: player.eliminated_at_hand,
                rank: player.rank,
                joined_at: player.joined_at,
                player_name: player.player ? player.player.nick_name : '',
                avatar: player.player ? player.player.avatar : '',
                level: player.player ? player.player.level : 0,
                vippoint: player.player ? player.player.vip_point : 0,
                exp: player.player ? player.player.exp : 0
            }));
        });
    }

    // Send notification to all players
    if (tournament.players && tournament.players.length > 0) {
        self.sendPushMessage(channelService, tournament.id.toString(), {
            route: consts.GAME.ROUTER.SNG_TOURNAMENT_STATUS,
            tournament: dto,
            players: playerDTOs
        }, function(err) {
            if (!err) {
                logger.info('SNG Tournament status notification sent:', {
                    tournament_id: tournament.id
                });
            }
        });
    }
};

/**
 * Notify players of blind increase
 */
SngTournamentService.prototype.notifyBlindIncrease = function(tournamentId, level, blindLevel) {
    var self = this;
    var channelService = self.app.get('channelService');
    logger.info('[sngTournamentService.notifyBlindIncrease] channelService: ', channelService);
    // Get active players count
    models.SngTournamentPlayer.count({
        where: {
            tournament_id: tournamentId,
            status: 'ACTIVE'
        }
    }).then(function(activePlayersCount) {
        // Get tournament info
        models.SngTournament.findByPk(tournamentId).then(function(tournament) {
            // Calculate next blind level timing
            var durationSeconds = blindLevel.duration_seconds ||
                (sngConsts.DEFAULT_SETTINGS.BLIND_INCREASE_MINUTES * 60);

            var nextLevelTime = new Date();
            nextLevelTime.setSeconds(nextLevelTime.getSeconds() + durationSeconds);

            // Send notification to all players
            if (channelService) {
                var channel = channelService.getChannel(tournamentId.toString(), true);

                if (channel) {
                    // Get all members in the channel and format them for pushMessageByUids
                    var members = channel.getMembers();
                    var receivers = [];

                    for (var memberId in members) {
                        var member = members[memberId];
                        if (member && member.uid && member.sid) {
                            receivers.push({
                                uid: member.uid,
                                sid: member.sid
                            });
                        }
                    }

                    if (receivers.length > 0) {
                        channelService.pushMessageByUids({
                            route: consts.GAME.ROUTER.SNG_TOURNAMENT_BLIND_UPDATE,
                            tournament_id: tournamentId,
                            level: level,
                            small_blind: blindLevel.small_blind || blindLevel.smallBlind,
                            big_blind: blindLevel.big_blind || blindLevel.bigBlind,
                            ante: blindLevel.ante || 0,
                            active_players: activePlayersCount,
                            total_players: tournament ? tournament.player_capacity : 0,
                            duration_seconds: durationSeconds,
                            next_level_time: nextLevelTime.toISOString()
                        }, receivers, function(err) {
                            if (err) {
                                logger.error('Error sending SNG Tournament blind update notification:', err);
                            } else {
                                logger.info('SNG Tournament blind update notification sent:', {
                                    tournament_id: tournamentId,
                                    level: level,
                                    receivers_count: receivers.length
                                });
                            }
                        });
                    } else {
                        logger.warn('No receivers found for blind increase notification:', {
                            tournament_id: tournamentId
                        });
                    }
                }
            } else {
                logger.warn('Channel service not available for blind increase notification:', {
                    tournament_id: tournamentId
                });
            }

            logger.info('Blind increase notification sent:', {
                tournament_id: tournamentId,
                level: level,
                small_blind: blindLevel.small_blind || blindLevel.smallBlind,
                big_blind: blindLevel.big_blind || blindLevel.bigBlind,
                ante: blindLevel.ante || 0,
                active_players: activePlayersCount
            });
        }).catch(function(err) {
            logger.error('Error getting tournament info for blind increase notification:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);

            // Send basic notification as fallback
            if (channelService) {
                var channel = channelService.getChannel(tournamentId.toString(), true);

                if (channel) {
                    // Get all members in the channel and format them for pushMessageByUids
                    var members = channel.getMembers();
                    var receivers = [];

                    for (var memberId in members) {
                        var member = members[memberId];
                        if (member && member.uid && member.sid) {
                            receivers.push({
                                uid: member.uid,
                                sid: member.sid
                            });
                        }
                    }

                    if (receivers.length > 0) {
                        channelService.pushMessageByUids({
                            route: consts.GAME.ROUTER.SNG_TOURNAMENT_BLIND_UPDATE,
                            tournament_id: tournamentId,
                            level: level,
                            small_blind: blindLevel.small_blind || blindLevel.smallBlind,
                            big_blind: blindLevel.big_blind || blindLevel.bigBlind,
                            ante: blindLevel.ante || 0
                        }, receivers, function(err) {
                            if (err) {
                                logger.error('Error sending fallback SNG Tournament blind update notification:', err);
                            }
                        });
                    }
                }
            }
        });
    }).catch(function(err) {
        logger.error('Error getting active players count for blind increase notification:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);

        // Send basic notification as fallback
        if (channelService) {
            var channel = channelService.getChannel(tournamentId.toString(), true);

            if (channel) {
                // Get all members in the channel and format them for pushMessageByUids
                var members = channel.getMembers();
                var receivers = [];

                for (var memberId in members) {
                    var member = members[memberId];
                    if (member && member.uid && member.sid) {
                        receivers.push({
                            uid: member.uid,
                            sid: member.sid
                        });
                    }
                }

                if (receivers.length > 0) {
                    channelService.pushMessageByUids({
                        route: consts.GAME.ROUTER.SNG_TOURNAMENT_BLIND_UPDATE,
                        tournament_id: tournamentId,
                        level: level,
                        small_blind: blindLevel.small_blind || blindLevel.smallBlind,
                        big_blind: blindLevel.big_blind || blindLevel.bigBlind,
                        ante: blindLevel.ante || 0
                    }, receivers, function(err) {
                        if (err) {
                            logger.error('Error sending final fallback SNG Tournament blind update notification:', err);
                        }
                    });
                }
            }
        }
    });
};

/**
 * Handle player elimination from tournament
 */
SngTournamentService.prototype.handlePlayerElimination = function(tournamentId, playerId, handNumber) {
    var self = this;

    // Get tournament
    models.SngTournament.findByPk(tournamentId, {
        include: [
            {
                model: models.SngTournamentPlayer,
                as: 'players',
                include: [
                    {
                        model: models.Player,
                        as: 'player'
                    }
                ]
            }
        ]
    }).then(function(tournament) {
        if (!tournament || tournament.status !== 'IN_PROGRESS') {
            return;
        }

        // Find player in tournament
        var tournamentPlayer = _.find(tournament.players, function(player) {
            return player.player_id === playerId;
        });

        if (!tournamentPlayer) {
            logger.error('Player not found in tournament:', playerId);
            return;
        }

        // Check if player is already eliminated
        if (tournamentPlayer.status === 'ELIMINATED' || tournamentPlayer.status === 'WINNER') {
            logger.info('Player already eliminated or winner:', playerId);
            return;
        }

        // Update player status
        tournamentPlayer.status = 'ELIMINATED';
        tournamentPlayer.eliminated_at_hand = handNumber;
        tournamentPlayer.current_chips = 0;

        // Get all players sorted by status (ACTIVE first, then ELIMINATED)
        var sortedPlayers = _.sortBy(tournament.players, function(player) {
            return player.status === 'ACTIVE' ? 0 : 1;
        });

        // Count active players
        var activePlayers = _.filter(sortedPlayers, function(player) {
            return player.status === 'ACTIVE';
        });

        // Calculate player rank
        // Rank is based on number of active players + 1
        tournamentPlayer.rank = activePlayers.length + 1;

        logger.info('Player eliminated with rank:', {
            player_id: playerId,
            rank: tournamentPlayer.rank,
            active_players: activePlayers.length,
            total_players: tournament.players.length
        });

        // Save the eliminated player
        tournamentPlayer.save().then(function() {
            // Log player elimination
            self.logTournamentAction(playerId, tournamentId, logBoards.SNG_PLAYER_ELIMINATED, {
                hand_number: handNumber,
                rank: tournamentPlayer.rank,
                active_players_remaining: activePlayers.length
            });

            // Notify players of elimination
            self.notifyPlayerElimination(tournament, tournamentPlayer);

            // Check if tournament should end (only one player left)
            if (activePlayers.length === 1) {
                // Last player is the winner
                var winner = activePlayers[0];

                // Update winner status
                winner.status = 'WINNER';
                winner.rank = 1;

                logger.info('Tournament winner determined:', {
                    tournament_id: tournamentId,
                    player_id: winner.player_id,
                    player_name: winner.player ? winner.player.nick_name : 'Unknown'
                });

                winner.save().then(function() {
                    // End tournament
                    self.endTournament(tournamentId);
                }).catch(function(err) {
                    logger.error('Error updating winner status:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
                });
            }
        }).catch(function(err) {
            logger.error('Error updating player elimination status:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
        });
    }).catch(function(err) {
        logger.error('Error handling player elimination:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
    });
};

/**
 * Notify players of player elimination
 */
SngTournamentService.prototype.notifyPlayerElimination = function(tournament, eliminatedPlayer) {
    var self = this;
    var channelService = self.app.get('channelService');
    logger.info('[sngTournamentService.notifyPlayerElimination] channelService: ', channelService);
    // Create player DTO
    var playerDTO = new SngTournamentPlayerDTO({
        id: eliminatedPlayer.id,
        tournament_id: eliminatedPlayer.tournament_id,
        player_id: eliminatedPlayer.player_id,
        seat_number: eliminatedPlayer.seat_number,
        initial_chips: eliminatedPlayer.initial_chips,
        current_chips: eliminatedPlayer.current_chips,
        status: eliminatedPlayer.status,
        eliminated_at_hand: eliminatedPlayer.eliminated_at_hand,
        rank: eliminatedPlayer.rank,
        joined_at: eliminatedPlayer.joined_at,
        player_name: eliminatedPlayer.player ? eliminatedPlayer.player.nick_name : '',
        avatar: eliminatedPlayer.player ? eliminatedPlayer.player.avatar : '',
        level: eliminatedPlayer.player ? eliminatedPlayer.player.level : 0,
        vippoint: eliminatedPlayer.player ? eliminatedPlayer.player.vip_point : 0,
        exp: eliminatedPlayer.player ? eliminatedPlayer.player.exp : 0
    });

    // Get remaining players count
    var activePlayers = _.filter(tournament.players, function(player) {
        return player.status === 'ACTIVE';
    });

    // Get current blind level
    var currentBlindLevel = self.getCurrentBlindLevel(tournament.id);
    var blindLevelInfo = self.getCurrentBlindLevelInfo(tournament.id);

    // Send notification to all players
    self.sendPushMessage(channelService, tournament.id.toString(), {
        route: consts.GAME.ROUTER.SNG_TOURNAMENT_PLAYER_ELIMINATED,
        tournament_id: tournament.id,
        player: playerDTO,
        remaining_players: activePlayers.length,
        total_players: tournament.players.length,
        current_blind_level: currentBlindLevel,
        small_blind: blindLevelInfo.small_blind || blindLevelInfo.smallBlind,
        big_blind: blindLevelInfo.big_blind || blindLevelInfo.bigBlind,
        ante: blindLevelInfo.ante
    }, function(err) {
        if (!err) {
            logger.info('SNG Tournament player elimination notification sent:', {
                tournament_id: tournament.id,
                player_id: eliminatedPlayer.player_id
            });
        }
    });

    logger.info('Player elimination notification sent:', {
        tournament_id: tournament.id,
        player_id: eliminatedPlayer.player_id,
        player_name: eliminatedPlayer.player ? eliminatedPlayer.player.nick_name : 'Unknown',
        rank: eliminatedPlayer.rank,
        remaining_players: activePlayers.length
    });
};

/**
 * End tournament and distribute rewards
 */
SngTournamentService.prototype.endTournament = function(tournamentId) {
    var self = this;

    logger.info('Ending tournament:', tournamentId);

    // Get tournament with all related data
    models.SngTournament.findByPk(tournamentId, {
        include: [
            {
                model: models.SngTournamentPlayer,
                as: 'players',
                include: [
                    {
                        model: models.Player,
                        as: 'player'
                    }
                ]
            },
            {
                model: models.SngBlindLevel,
                as: 'blindLevels'
            }
        ]
    }).then(function(tournament) {
        if (!tournament) {
            logger.error('Tournament not found:', tournamentId);
            return;
        }

        if (tournament.status === 'ENDED') {
            logger.info('Tournament already ended:', tournamentId);
            return;
        }

        // Calculate tournament statistics
        var tournamentDuration = tournament.started_at ?
            Math.floor((new Date() - tournament.started_at) / (1000 * 60)) : 0;

        var playerStats = [];
        if (tournament.players && tournament.players.length > 0) {
            // Sort players by rank
            var sortedPlayers = _.sortBy(tournament.players, 'rank');

            // Collect player statistics
            sortedPlayers.forEach(function(player) {
                playerStats.push({
                    player_id: player.player_id,
                    player_name: player.player ? player.player.nick_name : 'Unknown',
                    rank: player.rank,
                    status: player.status,
                    eliminated_at_hand: player.eliminated_at_hand
                });
            });
        }

        // Update tournament status
        tournament.status = 'ENDED';
        tournament.ended_at = new Date();

        tournament.save().then(function() {
            logger.info('Tournament status updated to ENDED:', tournamentId);

            // Stop blind timer
            if (self.blindTimers[tournamentId]) {
                clearTimeout(self.blindTimers[tournamentId]);
                delete self.blindTimers[tournamentId];
                logger.info('Blind timer stopped for tournament:', tournamentId);
            }

            // Distribute rewards
            self.distributeRewards(tournament);

            // Clean up tournament resources
            delete self.tournamentTables[tournamentId];
            delete self.tournaments[tournamentId];

            // Remove player-tournament mappings
            tournament.players.forEach(function(player) {
                delete self.playerTournaments[player.player_id];
            });

            // Log tournament end with detailed statistics
            self.logTournamentAction(null, tournamentId, logBoards.SNG_TOURNAMENT_END, {
                reward_pool: tournament.reward_pool,
                player_count: tournament.players.length,
                duration_minutes: tournamentDuration,
                started_at: tournament.started_at,
                ended_at: tournament.ended_at,
                player_stats: playerStats,
                final_blind_level: self.getCurrentBlindLevel(tournamentId)
            });

            logger.info('Tournament ended successfully:', {
                tournament_id: tournamentId,
                duration_minutes: tournamentDuration,
                player_count: tournament.players.length,
                reward_pool: tournament.reward_pool
            });

            // Notify players of tournament end
            self.notifyTournamentEnd(tournament);
        }).catch(function(err) {
            logger.error('Error updating tournament status:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
        });
    }).catch(function(err) {
        logger.error('Error ending tournament:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
    });
};

/**
 * Distribute rewards to tournament players
 */
SngTournamentService.prototype.distributeRewards = function(tournament) {
    var self = this;

    // Sort players by rank
    var sortedPlayers = _.sortBy(tournament.players, 'rank');

    // Calculate rewards based on distribution percentages
    var rewardPromises = [];
    var rewardDistribution = sngConsts.REWARD_DISTRIBUTION;

    // Only distribute rewards to top 3 players (or fewer if tournament had fewer players)
    var rewardCount = Math.min(3, sortedPlayers.length);

    // Adjust reward distribution based on number of players
    var adjustedDistribution = {};
    if (rewardCount === 1) {
        // If only one player, they get 100%
        adjustedDistribution = {
            1: 100
        };
    } else if (rewardCount === 2) {
        // If only two players, adjust to 70/30
        adjustedDistribution = {
            1: 70,
            2: 30
        };
    } else {
        // Default distribution for 3 or more players
        adjustedDistribution = {
            1: rewardDistribution.FIRST_PLACE,
            2: rewardDistribution.SECOND_PLACE,
            3: rewardDistribution.THIRD_PLACE
        };
    }

    // Log the reward distribution
    logger.info('SNG Tournament reward distribution:', adjustedDistribution);
    logger.info('SNG Tournament reward pool:', tournament.reward_pool);

    // Create a function to handle player reward updates
    // This avoids closure issues with the player variable in the Promise
    function createPlayerRewardPromises(player, rewardAmount, rewardPercentage) {
        // Create reward record
        var createRewardPromise = models.SngReward.create({
            tournament_id: tournament.id,
            player_id: player.player_id,
            rank: player.rank,
            reward_amount: rewardAmount,
            rewarded_at: new Date()
        });

        // Add reward to player's balance
        var updateBalancePromise = models.Player.findByPk(player.player_id).then(function(playerModel) {
            if (playerModel) {
                playerModel.balance += rewardAmount;
                return playerModel.save();
            }
        });

        // Log reward
        self.logTournamentAction(player.player_id, tournament.id, logBoards.SNG_REWARD, {
            rank: player.rank,
            reward_amount: rewardAmount,
            percentage: rewardPercentage
        });

        // Add log into table: transactions
        models.Transactions.create({
            player_id: player.player_id,
            amount: refundAmount,
            before_balance: currentBalance,
            after_balance: player.balance,
            type: 'SNG_TOURNAMENT',
            action: logBoards.SNG_REWARD,
            reference_id: tournament.id,
            reference_type: 'SNG_TOURNAMENT',
            meta: {
                rank: player.rank,
                reward_amount: rewardAmount,
                percentage: rewardPercentage
            },
            description: 'Reward for SNG Tournament',
        });

        return [createRewardPromise, updateBalancePromise];
    }

    // Process each player's reward
    for (var i = 0; i < rewardCount; i++) {
        var player = sortedPlayers[i];
        var rewardPercentage = adjustedDistribution[player.rank] || 0;

        // Calculate reward amount
        var rewardAmount = Math.floor((tournament.reward_pool * rewardPercentage) / 100);

        if (rewardAmount > 0) {
            logger.info('SNG Tournament reward for player:', {
                player_id: player.player_id,
                rank: player.rank,
                percentage: rewardPercentage,
                amount: rewardAmount
            });

            // Add reward promises for this player
            var playerPromises = createPlayerRewardPromises(player, rewardAmount, rewardPercentage);
            rewardPromises = rewardPromises.concat(playerPromises);
        }
    }

    // Execute all reward operations
    if (rewardPromises.length > 0) {
        Promise.all(rewardPromises)
            .then(function() {
                logger.info('SNG Tournament rewards distributed successfully for tournament:', tournament.id);
            })
            .catch(function(err) {
                logger.error('Error distributing tournament rewards:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
            });
    } else {
        logger.warn('No rewards to distribute for tournament:', tournament.id);
    }
};

/**
 * Create a similar tournament to replace one that has started
 * @param {Object} tournament - The tournament that has started
 */
SngTournamentService.prototype.createSimilarTournament = function(tournament) {
    var self = this;

    logger.info('Creating similar tournament to replace started tournament:', tournament.id);

    // Determine tournament type and level from the original tournament
    var type;
    if (tournament.player_capacity === 5) {
        type = sngConsts.TOURNAMENT_TYPE.SNG_5;
    } else if (tournament.player_capacity === 9) {
        type = sngConsts.TOURNAMENT_TYPE.SNG_9;
    } else {
        logger.error('Unknown player capacity for tournament:', tournament.id);
        return;
    }

    // Determine level based on buy-in amount
    var level;
    if (tournament.buy_in === sngConsts.TOURNAMENT_FEES_5.BEGINNER.BUY_IN) {
        level = sngConsts.TOURNAMENT_LEVEL.BEGINNER;
    } else if (tournament.buy_in === sngConsts.TOURNAMENT_FEES_5.INTERMEDIATE.BUY_IN) {
        level = sngConsts.TOURNAMENT_LEVEL.INTERMEDIATE;
    } else if (tournament.buy_in === sngConsts.TOURNAMENT_FEES_5.ADVANCED.BUY_IN) {
        level = sngConsts.TOURNAMENT_LEVEL.ADVANCED;
    } else if (tournament.buy_in === sngConsts.TOURNAMENT_FEES_5.PRO.BUY_IN) {
        level = sngConsts.TOURNAMENT_LEVEL.PRO;
    } else {
        logger.error('Unknown buy-in amount for tournament:', tournament.id);
        return;
    }

    // First, try to acquire a lock to prevent multiple servers from creating tournaments simultaneously
    sequelize.query('SELECT GET_LOCK("sng_tournament_creation_' + type + '_' + level + '", 5) as lockResult', {
        type: sequelize.QueryTypes.SELECT
    }).then(function(lockResult) {
        // Check if we got the lock (lockResult should be 1)
        if (!lockResult || !lockResult[0] || lockResult[0].lockResult !== 1) {
            logger.info('Could not acquire creation lock for type ' + type +
                ' and level ' + level + ', another server is probably creating a tournament');
            return;
        }

        logger.info('Acquired creation lock for type ' + type + ' and level ' + level);

        // Now check if we need to create a new tournament
        // Check how many tournaments of this type and level already exist
        models.SngTournament.count({
            where: {
                player_capacity: tournament.player_capacity,
                buy_in: tournament.buy_in,
                fee: tournament.fee
            }
        }).then(function(totalCount) {
            // Check if we've already reached the maximum number of tournaments for this type/level
            var maxTournaments = sngConsts.DEFAULT_SETTINGS.MAX_TOURNAMENTS_PER_TYPE_LEVEL;
            if (totalCount >= maxTournaments) {
                logger.info('Maximum number of tournaments (' + maxTournaments +
                    ') already reached for type ' + type + ' and level ' + level + '. Not creating a replacement tournament.');

                // Release the lock
                sequelize.query('SELECT RELEASE_LOCK("sng_tournament_creation_' + type + '_' + level + '")').then(function() {
                    logger.info('Released creation lock for type ' + type + ' and level ' + level);
                });

                return;
            }

            // Check if there is already an available tournament of this type and level in WAITING state
            models.SngTournament.count({
                where: {
                    player_capacity: tournament.player_capacity,
                    buy_in: tournament.buy_in,
                    fee: tournament.fee,
                    status: 'WAITING'
                }
            }).then(function(waitingCount) {
                // If there's already a waiting tournament, no need to create another one
                if (waitingCount > 0) {
                    logger.info('There is already a waiting tournament for type ' +
                        type + ' and level ' + level + '. Not creating a replacement tournament.');

                    // Release the lock
                    sequelize.query('SELECT RELEASE_LOCK("sng_tournament_creation_' + type + '_' + level + '")').then(function() {
                        logger.info('Released creation lock for type ' + type + ' and level ' + level);
                    });

                    return;
                }

                // Create options for the new tournament
                var options = {
                    type: type,
                    level: level,
                    player_capacity: tournament.player_capacity,
                    buy_in: tournament.buy_in,
                    fee: tournament.fee,
                    initial_chips: tournament.initial_chips,
                    name: tournament.name
                };

                // Create the new tournament
                self.createTournament(options, function(err, newTournament) {
                    if (err) {
                        logger.error('Error creating similar tournament:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
                    } else {
                        logger.info('Similar tournament created successfully:', newTournament.id);
                    }

                    // Release the lock
                    sequelize.query('SELECT RELEASE_LOCK("sng_tournament_creation_' + type + '_' + level + '")').then(function() {
                        logger.info('Released creation lock for type ' + type + ' and level ' + level + ' after creating tournament');
                    });
                });
            }).catch(function(err) {
                logger.error('Error checking waiting tournaments:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);

                // Release the lock
                sequelize.query('SELECT RELEASE_LOCK("sng_tournament_creation_' + type + '_' + level + '")').then(function() {
                    logger.info('Released creation lock for type ' + type + ' and level ' + level + ' after error');
                });
            });
        }).catch(function(err) {
            logger.error('Error checking total tournaments:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);

            // Release the lock
            sequelize.query('SELECT RELEASE_LOCK("sng_tournament_creation_' + type + '_' + level + '")').then(function() {
                logger.info('Released creation lock for type ' + type + ' and level ' + level + ' after error');
            });
        });
    }).catch(function(err) {
        logger.error('Error acquiring creation lock for type ' + type + ' and level ' + level + ':', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
    });
};

/**
 * Check if SNG tournaments have already been initialized
 * @param {function} callback - Callback function with boolean result
 */
SngTournamentService.prototype.checkInitializationStatus = function(callback) {
    // Check if there's a system flag indicating initialization is complete
    models.SngTournament.findOne({
        where: {
            code: 'SYSTEM_INIT_FLAG',
            status: 'SYSTEM'
        }
    }).then(function(initFlag) {
        if (initFlag) {
            // Initialization flag exists, so initialization has been done
            callback(true);
            return;
        }

        // No initialization flag, check if we already have the expected number of tournaments
        // Count tournaments for each type and level
        var countPromises = [];
        var tournamentTypes = [
            sngConsts.TOURNAMENT_TYPE.SNG_5,
            sngConsts.TOURNAMENT_TYPE.SNG_9
        ];
        var tournamentLevels = [
            sngConsts.TOURNAMENT_LEVEL.BEGINNER,
            sngConsts.TOURNAMENT_LEVEL.INTERMEDIATE,
            sngConsts.TOURNAMENT_LEVEL.ADVANCED,
            sngConsts.TOURNAMENT_LEVEL.PRO
        ];

        // For each type and level combination, check if there's at least one tournament
        tournamentTypes.forEach(function(type) {
            var playerCapacity = type === sngConsts.TOURNAMENT_TYPE.SNG_5 ? 5 : 9;

            tournamentLevels.forEach(function(level) {
                var buyIn, fee;

                // Determine buy-in and fee based on level and type
                if (type === sngConsts.TOURNAMENT_TYPE.SNG_5) {
                    switch (level) {
                        case sngConsts.TOURNAMENT_LEVEL.BEGINNER:
                            buyIn = sngConsts.TOURNAMENT_FEES_5.BEGINNER.BUY_IN;
                            fee = sngConsts.TOURNAMENT_FEES_5.BEGINNER.FEE;
                            break;
                        case sngConsts.TOURNAMENT_LEVEL.INTERMEDIATE:
                            buyIn = sngConsts.TOURNAMENT_FEES_5.INTERMEDIATE.BUY_IN;
                            fee = sngConsts.TOURNAMENT_FEES_5.INTERMEDIATE.FEE;
                            break;
                        case sngConsts.TOURNAMENT_LEVEL.ADVANCED:
                            buyIn = sngConsts.TOURNAMENT_FEES_5.ADVANCED.BUY_IN;
                            fee = sngConsts.TOURNAMENT_FEES_5.ADVANCED.FEE;
                            break;
                        case sngConsts.TOURNAMENT_LEVEL.PRO:
                            buyIn = sngConsts.TOURNAMENT_FEES_5.PRO.BUY_IN;
                            fee = sngConsts.TOURNAMENT_FEES_5.PRO.FEE;
                            break;
                    }
                } else {
                    switch (level) {
                        case sngConsts.TOURNAMENT_LEVEL.BEGINNER:
                            buyIn = sngConsts.TOURNAMENT_FEES_9.BEGINNER.BUY_IN;
                            fee = sngConsts.TOURNAMENT_FEES_9.BEGINNER.FEE;
                            break;
                        case sngConsts.TOURNAMENT_LEVEL.INTERMEDIATE:
                            buyIn = sngConsts.TOURNAMENT_FEES_9.INTERMEDIATE.BUY_IN;
                            fee = sngConsts.TOURNAMENT_FEES_9.INTERMEDIATE.FEE;
                            break;
                        case sngConsts.TOURNAMENT_LEVEL.ADVANCED:
                            buyIn = sngConsts.TOURNAMENT_FEES_9.ADVANCED.BUY_IN;
                            fee = sngConsts.TOURNAMENT_FEES_9.ADVANCED.FEE;
                            break;
                        case sngConsts.TOURNAMENT_LEVEL.PRO:
                            buyIn = sngConsts.TOURNAMENT_FEES_9.PRO.BUY_IN;
                            fee = sngConsts.TOURNAMENT_FEES_9.PRO.FEE;
                            break;
                    }
                }

                // Count tournaments for this type/level
                countPromises.push(
                    models.SngTournament.count({
                        where: {
                            player_capacity: playerCapacity,
                            buy_in: buyIn,
                            fee: fee
                        }
                    }).then(function(count) {
                        return {
                            type: type,
                            level: level,
                            count: count
                        };
                    })
                );
            });
        });

        // Check all counts
        Promise.all(countPromises).then(function(results) {
            // If we have at least one tournament for each type/level, consider initialization done
            var allTypesHaveTournaments = true;
            var totalTournaments = 0;

            results.forEach(function(result) {
                totalTournaments += result.count;
                if (result.count === 0) {
                    allTypesHaveTournaments = false;
                }
            });

            // If we have tournaments for all types/levels, create the initialization flag
            if (allTypesHaveTournaments) {
                // Create initialization flag
                models.SngTournament.create({
                    code: 'SYSTEM_INIT_FLAG',
                    status: 'SYSTEM',
                    player_capacity: 0,
                    buy_in: 0,
                    fee: 0,
                    reward_pool: 0,
                    created_at: new Date()
                }).then(function() {
                    logger.info('Created SNG initialization flag. Total tournaments: ' + totalTournaments);
                    callback(true);
                }).catch(function(err) {
                    logger.error('Error creating initialization flag:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
                    callback(allTypesHaveTournaments); // Still return true if we have all tournaments
                });
            } else {
                callback(false);
            }
        }).catch(function(err) {
            logger.error('Error checking tournament counts:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
            callback(false);
        });
    }).catch(function(err) {
        logger.error('Error checking initialization status:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
        callback(false);
    });
};

/**
 * Ensure there are available tournaments for each type and level
 */
SngTournamentService.prototype.ensureAvailableTournaments = function() {
    var self = this;

    logger.info('Ensuring available tournaments for each type and level');

    // Get all tournament types and levels from configuration
    var tournamentTypes = [
        sngConsts.TOURNAMENT_TYPE.SNG_5,
        sngConsts.TOURNAMENT_TYPE.SNG_9
    ];

    var tournamentLevels = [
        sngConsts.TOURNAMENT_LEVEL.BEGINNER,
        sngConsts.TOURNAMENT_LEVEL.INTERMEDIATE,
        sngConsts.TOURNAMENT_LEVEL.ADVANCED,
        sngConsts.TOURNAMENT_LEVEL.PRO
    ];

    // Process tournament types and levels sequentially to avoid connection flooding
    var processQueue = [];

    tournamentTypes.forEach(function(type) {
        tournamentLevels.forEach(function(level) {
            processQueue.push({ type: type, level: level });
        });
    });

    // First, try to acquire a global lock to ensure only one server performs initialization
    sequelize.query('SELECT GET_LOCK("sng_tournament_initialization", 10) as lockResult', {
        type: sequelize.QueryTypes.SELECT
    }).then(function(lockResult) {
        // Check if we got the lock (lockResult should be 1)
        if (!lockResult || !lockResult[0] || lockResult[0].lockResult !== 1) {
            logger.info('Could not acquire initialization lock, another server is probably initializing');
            return;
        }

        logger.info('Acquired initialization lock');

        // Create a system flag to indicate initialization is in progress
        models.SngTournament.findOrCreate({
            where: {
                code: 'SYSTEM_INIT_IN_PROGRESS',
                status: 'SYSTEM'
            },
            defaults: {
                player_capacity: 0,
                buy_in: 0,
                fee: 0,
                reward_pool: 0,
                created_at: new Date()
            }
        }).then(function() {
            // Now check each type/level and create tournaments as needed
            var checkPromises = [];

            processQueue.forEach(function(item) {
                var playerCapacity = item.type === sngConsts.TOURNAMENT_TYPE.SNG_5 ? 5 : 9;
                var buyIn, fee;

                // Determine buy-in and fee based on level and type
                if (item.type === sngConsts.TOURNAMENT_TYPE.SNG_5) {
                    switch (item.level) {
                        case sngConsts.TOURNAMENT_LEVEL.BEGINNER:
                            buyIn = sngConsts.TOURNAMENT_FEES_5.BEGINNER.BUY_IN;
                            fee = sngConsts.TOURNAMENT_FEES_5.BEGINNER.FEE;
                            break;
                        case sngConsts.TOURNAMENT_LEVEL.INTERMEDIATE:
                            buyIn = sngConsts.TOURNAMENT_FEES_5.INTERMEDIATE.BUY_IN;
                            fee = sngConsts.TOURNAMENT_FEES_5.INTERMEDIATE.FEE;
                            break;
                        case sngConsts.TOURNAMENT_LEVEL.ADVANCED:
                            buyIn = sngConsts.TOURNAMENT_FEES_5.ADVANCED.BUY_IN;
                            fee = sngConsts.TOURNAMENT_FEES_5.ADVANCED.FEE;
                            break;
                        case sngConsts.TOURNAMENT_LEVEL.PRO:
                            buyIn = sngConsts.TOURNAMENT_FEES_5.PRO.BUY_IN;
                            fee = sngConsts.TOURNAMENT_FEES_5.PRO.FEE;
                            break;
                    }
                } else {
                    switch (item.level) {
                        case sngConsts.TOURNAMENT_LEVEL.BEGINNER:
                            buyIn = sngConsts.TOURNAMENT_FEES_9.BEGINNER.BUY_IN;
                            fee = sngConsts.TOURNAMENT_FEES_9.BEGINNER.FEE;
                            break;
                        case sngConsts.TOURNAMENT_LEVEL.INTERMEDIATE:
                            buyIn = sngConsts.TOURNAMENT_FEES_9.INTERMEDIATE.BUY_IN;
                            fee = sngConsts.TOURNAMENT_FEES_9.INTERMEDIATE.FEE;
                            break;
                        case sngConsts.TOURNAMENT_LEVEL.ADVANCED:
                            buyIn = sngConsts.TOURNAMENT_FEES_9.ADVANCED.BUY_IN;
                            fee = sngConsts.TOURNAMENT_FEES_9.ADVANCED.FEE;
                            break;
                        case sngConsts.TOURNAMENT_LEVEL.PRO:
                            buyIn = sngConsts.TOURNAMENT_FEES_9.PRO.BUY_IN;
                            fee = sngConsts.TOURNAMENT_FEES_9.PRO.FEE;
                            break;
                    }
                }

                // Check if there's already a WAITING tournament for this type/level
                checkPromises.push(
                    models.SngTournament.count({
                        where: {
                            player_capacity: playerCapacity,
                            buy_in: buyIn,
                            fee: fee,
                            status: 'WAITING'
                        }
                    }).then(function(waitingCount) {
                        return {
                            type: item.type,
                            level: item.level,
                            playerCapacity: playerCapacity,
                            buyIn: buyIn,
                            fee: fee,
                            waitingCount: waitingCount
                        };
                    })
                );
            });

            // Process all checks
            Promise.all(checkPromises).then(function(results) {
                // Determine which tournaments need to be created
                var toCreate = [];

                results.forEach(function(result) {
                    if (result.waitingCount === 0) {
                        logger.info('No waiting tournaments found for type ' + result.type +
                            ' and level ' + result.level + '. Will create one.');
                        toCreate.push(result);
                    } else {
                        logger.info('Found ' + result.waitingCount +
                            ' waiting tournament(s) for type ' + result.type + ' and level ' + result.level + '. No need to create more.');
                    }
                });

                // If no tournaments need to be created, we're done
                if (toCreate.length === 0) {
                    logger.info('All tournament types and levels have waiting tournaments. No need to create any.');

                    // Create the initialization complete flag
                    models.SngTournament.findOrCreate({
                        where: {
                            code: 'SYSTEM_INIT_FLAG',
                            status: 'SYSTEM'
                        },
                        defaults: {
                            player_capacity: 0,
                            buy_in: 0,
                            fee: 0,
                            reward_pool: 0,
                            created_at: new Date()
                        }
                    }).then(function() {
                        // Release the lock
                        sequelize.query('SELECT RELEASE_LOCK("sng_tournament_initialization")').then(function() {
                            logger.info('Released initialization lock');
                        });
                    });

                    return;
                }

                // Create tournaments sequentially
                logger.info('Need to create ' + toCreate.length + ' tournaments. Creating sequentially...');

                var createNextTournament = function(index) {
                    if (index >= toCreate.length) {
                        // All tournaments created, set initialization flag and release lock
                        models.SngTournament.findOrCreate({
                            where: {
                                code: 'SYSTEM_INIT_FLAG',
                                status: 'SYSTEM'
                            },
                            defaults: {
                                player_capacity: 0,
                                buy_in: 0,
                                fee: 0,
                                reward_pool: 0,
                                created_at: new Date()
                            }
                        }).then(function() {
                            // Release the lock
                            sequelize.query('SELECT RELEASE_LOCK("sng_tournament_initialization")').then(function() {
                                logger.info('Released initialization lock after creating all tournaments');
                            });
                        });

                        return;
                    }

                    var item = toCreate[index];

                    // Find tournament configuration
                    var tournamentConfig = null;
                    if (self.tournamentConfigs && self.tournamentConfigs.tournaments) {
                        tournamentConfig = self.tournamentConfigs.tournaments.find(function(config) {
                            return (config.player_capacity === item.playerCapacity) &&
                                   (config.level === item.level);
                        });
                    }

                    // Create options for the new tournament
                    var options = {
                        type: item.type,
                        level: item.level,
                        player_capacity: item.playerCapacity,
                        buy_in: item.buyIn,
                        fee: item.fee
                    };

                    // Add name if available from config
                    if (tournamentConfig && tournamentConfig.name) {
                        options.name = tournamentConfig.name;
                    }

                    logger.info('Creating tournament for type ' + item.type + ' and level ' + item.level);

                    self.createTournament(options, function(err, newTournament) {
                        if (err) {
                            logger.error('Error creating tournament for type ' +
                                item.type + ' and level ' + item.level + ':', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
                        } else {
                            logger.info('Tournament created successfully for type ' +
                                item.type + ' and level ' + item.level + ':', newTournament.id);
                        }

                        // Small delay to prevent connection flooding
                        setTimeout(function() {
                            createNextTournament(index + 1);
                        }, 200);
                    });
                };

                // Start creating tournaments
                createNextTournament(0);
            }).catch(function(err) {
                logger.error('Error checking tournament counts:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);

                // Release the lock
                sequelize.query('SELECT RELEASE_LOCK("sng_tournament_initialization")').then(function() {
                    logger.info('Released initialization lock after error');
                });
            });
        }).catch(function(err) {
            logger.error('Error creating in-progress flag:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);

            // Release the lock
            sequelize.query('SELECT RELEASE_LOCK("sng_tournament_initialization")').then(function() {
                logger.info('Released initialization lock after error');
            });
        });
    }).catch(function(err) {
        logger.error('Error acquiring initialization lock:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
    });
};

/**
 * Find an available tournament by type and level
 * @param {string} type - Tournament type (5_PLAYERS or 9_PLAYERS)
 * @param {string} level - Tournament level (BEGINNER, INTERMEDIATE, ADVANCED, PRO)
 * @param {function} callback - Callback function with (error, tournamentId)
 */
SngTournamentService.prototype.findAvailableTournamentByTypeAndLevel = function(type, level, callback) {
    var self = this;

    // Validate type and level
    if (!Object.values(sngConsts.TOURNAMENT_TYPE).includes(type)) {
        return callback(new Error('Invalid tournament type: ' + type));
    }

    if (!Object.values(sngConsts.TOURNAMENT_LEVEL).includes(level)) {
        return callback(new Error('Invalid tournament level: ' + level));
    }

    // Determine player capacity based on type
    var playerCapacity = type === sngConsts.TOURNAMENT_TYPE.SNG_5 ? 5 : 9;

    // Determine buy-in and fee based on type and level
    var buyIn, fee;
    if (type === sngConsts.TOURNAMENT_TYPE.SNG_5) {
        switch (level) {
            case sngConsts.TOURNAMENT_LEVEL.BEGINNER:
                buyIn = sngConsts.TOURNAMENT_FEES_5.BEGINNER.BUY_IN;
                fee = sngConsts.TOURNAMENT_FEES_5.BEGINNER.FEE;
                break;
            case sngConsts.TOURNAMENT_LEVEL.INTERMEDIATE:
                buyIn = sngConsts.TOURNAMENT_FEES_5.INTERMEDIATE.BUY_IN;
                fee = sngConsts.TOURNAMENT_FEES_5.INTERMEDIATE.FEE;
                break;
            case sngConsts.TOURNAMENT_LEVEL.ADVANCED:
                buyIn = sngConsts.TOURNAMENT_FEES_5.ADVANCED.BUY_IN;
                fee = sngConsts.TOURNAMENT_FEES_5.ADVANCED.FEE;
                break;
            case sngConsts.TOURNAMENT_LEVEL.PRO:
                buyIn = sngConsts.TOURNAMENT_FEES_5.PRO.BUY_IN;
                fee = sngConsts.TOURNAMENT_FEES_5.PRO.FEE;
                break;
        }
    } else {
        switch (level) {
            case sngConsts.TOURNAMENT_LEVEL.BEGINNER:
                buyIn = sngConsts.TOURNAMENT_FEES_9.BEGINNER.BUY_IN;
                fee = sngConsts.TOURNAMENT_FEES_9.BEGINNER.FEE;
                break;
            case sngConsts.TOURNAMENT_LEVEL.INTERMEDIATE:
                buyIn = sngConsts.TOURNAMENT_FEES_9.INTERMEDIATE.BUY_IN;
                fee = sngConsts.TOURNAMENT_FEES_9.INTERMEDIATE.FEE;
                break;
            case sngConsts.TOURNAMENT_LEVEL.ADVANCED:
                buyIn = sngConsts.TOURNAMENT_FEES_9.ADVANCED.BUY_IN;
                fee = sngConsts.TOURNAMENT_FEES_9.ADVANCED.FEE;
                break;
            case sngConsts.TOURNAMENT_LEVEL.PRO:
                buyIn = sngConsts.TOURNAMENT_FEES_9.PRO.BUY_IN;
                fee = sngConsts.TOURNAMENT_FEES_9.PRO.FEE;
                break;
        }
    }

    // Find a tournament with matching criteria in WAITING status
    models.SngTournament.findOne({
        where: {
            player_capacity: playerCapacity,
            buy_in: buyIn,
            fee: fee,
            status: 'WAITING'
        },
        include: [
            {
                model: models.SngTournamentPlayer,
                as: 'players'
            }
        ],
        order: [
            ['created_at', 'ASC'] // Get the oldest tournament first
        ]
    }).then(function(tournament) {
        if (!tournament) {
            // No available tournament found, check if we need to create one
            self.checkAndCreateTournamentIfNeeded(type, level, function() {
                // Try to find a tournament again after potentially creating one
                models.SngTournament.findOne({
                    where: {
                        player_capacity: playerCapacity,
                        buy_in: buyIn,
                        fee: fee,
                        status: 'WAITING'
                    },
                    order: [
                        ['created_at', 'ASC'] // Get the oldest tournament first
                    ]
                }).then(function(newTournament) {
                    if (!newTournament) {
                        // Still no tournament available
                        logger.error('No available tournament found for type ' + type + ' and level ' + level + ' after creation attempt');
                        callback(null, null);
                        return;
                    }

                    // Return the tournament ID
                    logger.info('Found available tournament for type ' + type + ' and level ' + level + ': ' + newTournament.id);
                    callback(null, newTournament.id);
                }).catch(function(err) {
                    logger.error('Error finding tournament after creation:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
                    callback(err);
                });
            });
            return;
        }

        // Return the tournament ID
        logger.info('Found available tournament for type ' + type + ' and level ' + level + ': ' + tournament.id);
        callback(null, tournament.id);
    }).catch(function(err) {
        logger.error('Error finding available tournament:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
        callback(err);
    });
};

/**
 * Check if a tournament of the specified type and level exists and create one if needed
 * @param {string} type - Tournament type
 * @param {string} level - Tournament level
 * @param {function} callback - Callback function
 */
SngTournamentService.prototype.checkAndCreateTournamentIfNeeded = function(type, level, callback) {
    var self = this;

    // Determine player capacity based on type
    var playerCapacity = type === sngConsts.TOURNAMENT_TYPE.SNG_5 ? 5 : 9;

    // Determine buy-in and fee based on level and type
    var buyIn, fee;
    if (type === sngConsts.TOURNAMENT_TYPE.SNG_5) {
        switch (level) {
            case sngConsts.TOURNAMENT_LEVEL.BEGINNER:
                buyIn = sngConsts.TOURNAMENT_FEES_5.BEGINNER.BUY_IN;
                fee = sngConsts.TOURNAMENT_FEES_5.BEGINNER.FEE;
                break;
            case sngConsts.TOURNAMENT_LEVEL.INTERMEDIATE:
                buyIn = sngConsts.TOURNAMENT_FEES_5.INTERMEDIATE.BUY_IN;
                fee = sngConsts.TOURNAMENT_FEES_5.INTERMEDIATE.FEE;
                break;
            case sngConsts.TOURNAMENT_LEVEL.ADVANCED:
                buyIn = sngConsts.TOURNAMENT_FEES_5.ADVANCED.BUY_IN;
                fee = sngConsts.TOURNAMENT_FEES_5.ADVANCED.FEE;
                break;
            case sngConsts.TOURNAMENT_LEVEL.PRO:
                buyIn = sngConsts.TOURNAMENT_FEES_5.PRO.BUY_IN;
                fee = sngConsts.TOURNAMENT_FEES_5.PRO.FEE;
                break;
        }
    } else {
        switch (level) {
            case sngConsts.TOURNAMENT_LEVEL.BEGINNER:
                buyIn = sngConsts.TOURNAMENT_FEES_9.BEGINNER.BUY_IN;
                fee = sngConsts.TOURNAMENT_FEES_9.BEGINNER.FEE;
                break;
            case sngConsts.TOURNAMENT_LEVEL.INTERMEDIATE:
                buyIn = sngConsts.TOURNAMENT_FEES_9.INTERMEDIATE.BUY_IN;
                fee = sngConsts.TOURNAMENT_FEES_9.INTERMEDIATE.FEE;
                break;
            case sngConsts.TOURNAMENT_LEVEL.ADVANCED:
                buyIn = sngConsts.TOURNAMENT_FEES_9.ADVANCED.BUY_IN;
                fee = sngConsts.TOURNAMENT_FEES_9.ADVANCED.FEE;
                break;
            case sngConsts.TOURNAMENT_LEVEL.PRO:
                buyIn = sngConsts.TOURNAMENT_FEES_9.PRO.BUY_IN;
                fee = sngConsts.TOURNAMENT_FEES_9.PRO.FEE;
                break;
        }
    }

    // Check if there is already a WAITING tournament of this type and level
    models.SngTournament.count({
        where: {
            player_capacity: playerCapacity,
            buy_in: buyIn,
            fee: fee,
            status: 'WAITING'
        }
    }).then(function(waitingCount) {
        if (waitingCount > 0) {
            logger.info('Found ' + waitingCount + ' waiting tournament(s) for type ' + type + ' and level ' + level + '. No need to create more.');
            callback();
            return;
        }

        // No waiting tournaments found, check total count for this type/level
        models.SngTournament.count({
            where: {
                player_capacity: playerCapacity,
                buy_in: buyIn,
                fee: fee
            }
        }).then(function(totalCount) {
            var maxTournaments = sngConsts.DEFAULT_SETTINGS.MAX_TOURNAMENTS_PER_TYPE_LEVEL;

            if (totalCount >= maxTournaments) {
                logger.info('Maximum number of tournaments (' + maxTournaments + ') already reached for type ' + type + ' and level ' + level);
                callback();
                return;
            }

            // Create a new tournament
            logger.info('Creating a new tournament for type ' + type + ' and level ' + level);

            // Find tournament configuration
            var tournamentConfig = null;
            if (self.tournamentConfigs && self.tournamentConfigs.tournaments) {
                tournamentConfig = self.tournamentConfigs.tournaments.find(function(config) {
                    return (config.player_capacity === playerCapacity) &&
                           (config.level === level);
                });
            }

            // Create options for the new tournament
            var options = {
                type: type,
                level: level,
                player_capacity: playerCapacity,
                buy_in: buyIn,
                fee: fee
            };

            // Add name if available from config
            if (tournamentConfig && tournamentConfig.name) {
                options.name = tournamentConfig.name;
            }

            self.createTournament(options, function(err, newTournament) {
                if (err) {
                    logger.error('Error creating tournament for type ' + type + ' and level ' + level + ':', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
                } else {
                    logger.info('Tournament created successfully for type ' + type + ' and level ' + level + ':', newTournament.id);
                }
                callback();
            });
        }).catch(function(err) {
            logger.error('Error counting total tournaments:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
            callback();
        });
    }).catch(function(err) {
        logger.error('Error counting waiting tournaments:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
        callback();
    });
};

/**
 * Create an initial tournament for a specific type and level
 * @param {string} type - Tournament type
 * @param {string} level - Tournament level
 * @param {function} callback - Callback function
 */
SngTournamentService.prototype.createInitialTournament = function(type, level, callback) {
    var self = this;

    // Determine player capacity based on type
    var playerCapacity = type === sngConsts.TOURNAMENT_TYPE.SNG_5 ? 5 : 9;

    // Determine buy-in and fee based on level and type
    var buyIn, fee;
    if (type === sngConsts.TOURNAMENT_TYPE.SNG_5) {
        switch (level) {
            case sngConsts.TOURNAMENT_LEVEL.BEGINNER:
                buyIn = sngConsts.TOURNAMENT_FEES_5.BEGINNER.BUY_IN;
                fee = sngConsts.TOURNAMENT_FEES_5.BEGINNER.FEE;
                break;
            case sngConsts.TOURNAMENT_LEVEL.INTERMEDIATE:
                buyIn = sngConsts.TOURNAMENT_FEES_5.INTERMEDIATE.BUY_IN;
                fee = sngConsts.TOURNAMENT_FEES_5.INTERMEDIATE.FEE;
                break;
            case sngConsts.TOURNAMENT_LEVEL.ADVANCED:
                buyIn = sngConsts.TOURNAMENT_FEES_5.ADVANCED.BUY_IN;
                fee = sngConsts.TOURNAMENT_FEES_5.ADVANCED.FEE;
                break;
            case sngConsts.TOURNAMENT_LEVEL.PRO:
                buyIn = sngConsts.TOURNAMENT_FEES_5.PRO.BUY_IN;
                fee = sngConsts.TOURNAMENT_FEES_5.PRO.FEE;
                break;
        }
    } else {
        switch (level) {
            case sngConsts.TOURNAMENT_LEVEL.BEGINNER:
                buyIn = sngConsts.TOURNAMENT_FEES_9.BEGINNER.BUY_IN;
                fee = sngConsts.TOURNAMENT_FEES_9.BEGINNER.FEE;
                break;
            case sngConsts.TOURNAMENT_LEVEL.INTERMEDIATE:
                buyIn = sngConsts.TOURNAMENT_FEES_9.INTERMEDIATE.BUY_IN;
                fee = sngConsts.TOURNAMENT_FEES_9.INTERMEDIATE.FEE;
                break;
            case sngConsts.TOURNAMENT_LEVEL.ADVANCED:
                buyIn = sngConsts.TOURNAMENT_FEES_9.ADVANCED.BUY_IN;
                fee = sngConsts.TOURNAMENT_FEES_9.ADVANCED.FEE;
                break;
            case sngConsts.TOURNAMENT_LEVEL.PRO:
                buyIn = sngConsts.TOURNAMENT_FEES_9.PRO.BUY_IN;
                fee = sngConsts.TOURNAMENT_FEES_9.PRO.FEE;
                break;
        }
    }

    // Find tournament configuration
    var tournamentConfig = null;
    if (self.tournamentConfigs && self.tournamentConfigs.tournaments) {
        tournamentConfig = self.tournamentConfigs.tournaments.find(function(config) {
            return (config.player_capacity === playerCapacity) &&
                   (config.level === level);
        });
    }

    // Create options for the new tournament
    var options = {
        type: type,
        level: level,
        player_capacity: playerCapacity,
        buy_in: buyIn,
        fee: fee
    };

    // Add name if available from config
    if (tournamentConfig && tournamentConfig.name) {
        options.name = tournamentConfig.name;
    }

    self.createTournament(options, function(err, newTournament) {
        if (err) {
            logger.error('Error creating initial tournament for type ' + type + ' and level ' + level + ':', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
        } else {
            logger.info('Initial tournament created successfully for type ' + type + ' and level ' + level + ':', newTournament.id);
        }
        callback();
    });
};



/**
 * Notify players of tournament end
 */
SngTournamentService.prototype.notifyTournamentEnd = function(tournament) {
    var self = this;
    var channelService = self.app.get('channelService');
    logger.info('[sngTournamentService.notifyTournamentEnd] channelService: ', channelService);
    // Create tournament DTO
    var dto = new SngTournamentDTO({
        id: tournament.id,
        code: tournament.code,
        status: tournament.status,
        player_capacity: tournament.player_capacity,
        buy_in: tournament.buy_in,
        fee: tournament.fee,
        reward_pool: tournament.reward_pool,
        created_at: tournament.created_at,
        started_at: tournament.started_at,
        ended_at: tournament.ended_at
    });

    // Create player DTOs
    var playerDTOs = [];
    if (tournament.players && tournament.players.length > 0) {
        // Sort players by rank
        var sortedPlayers = _.sortBy(tournament.players, 'rank');

        sortedPlayers.forEach(function(player) {
            playerDTOs.push(new SngTournamentPlayerDTO({
                id: player.id,
                tournament_id: player.tournament_id,
                player_id: player.player_id,
                seat_number: player.seat_number,
                initial_chips: player.initial_chips,
                current_chips: player.current_chips,
                status: player.status,
                eliminated_at_hand: player.eliminated_at_hand,
                rank: player.rank,
                joined_at: player.joined_at,
                player_name: player.player ? player.player.nick_name : '',
                avatar: player.player ? player.player.avatar : '',
                level: player.player ? player.player.level : 0,
                vippoint: player.player ? player.player.vip_point : 0,
                exp: player.player ? player.player.exp : 0
            }));
        });
    }

    // Get actual rewards from database
    models.SngReward.findAll({
        where: {
            tournament_id: tournament.id
        },
        include: [
            {
                model: models.Player,
                as: 'player'
            }
        ]
    }).then(function(rewards) {
        // Create reward DTOs from actual rewards
        var rewardDTOs = [];

        if (rewards && rewards.length > 0) {
            rewards.forEach(function(reward) {
                rewardDTOs.push(new SngRewardDTO({
                    id: reward.id,
                    tournament_id: reward.tournament_id,
                    player_id: reward.player_id,
                    rank: reward.rank,
                    reward_amount: reward.reward_amount,
                    rewarded_at: reward.rewarded_at,
                    player_name: reward.player ? reward.player.nick_name : '',
                    avatar: reward.player ? reward.player.avatar : ''
                }));
            });
        } else {
            // If no rewards found in database, calculate them
            // This is a fallback in case the rewards haven't been saved yet

            // Calculate rewards based on distribution percentages
            var rewardDistribution = sngConsts.REWARD_DISTRIBUTION;

            // Only distribute rewards to top 3 players (or fewer if tournament had fewer players)
            var rewardCount = Math.min(3, playerDTOs.length);

            // Adjust reward distribution based on number of players
            var adjustedDistribution = {};
            if (rewardCount === 1) {
                // If only one player, they get 100%
                adjustedDistribution = {
                    1: 100
                };
            } else if (rewardCount === 2) {
                // If only two players, adjust to 70/30
                adjustedDistribution = {
                    1: 70,
                    2: 30
                };
            } else {
                // Default distribution for 3 or more players
                adjustedDistribution = {
                    1: rewardDistribution.FIRST_PLACE,
                    2: rewardDistribution.SECOND_PLACE,
                    3: rewardDistribution.THIRD_PLACE
                };
            }

            for (var i = 0; i < rewardCount; i++) {
                var player = playerDTOs[i];
                var rewardPercentage = adjustedDistribution[player.rank] || 0;

                // Calculate reward amount
                var rewardAmount = Math.floor((tournament.reward_pool * rewardPercentage) / 100);

                if (rewardAmount > 0) {
                    rewardDTOs.push(new SngRewardDTO({
                        tournament_id: tournament.id,
                        player_id: player.player_id,
                        rank: player.rank,
                        reward_amount: rewardAmount,
                        player_name: player.player_name,
                        avatar: player.avatar
                    }));
                }
            }
        }

        // Send notification to all players
        self.sendPushMessage(channelService, tournament.id.toString(), {
            route: consts.GAME.ROUTER.SNG_TOURNAMENT_RESULT,
            tournament: dto,
            players: playerDTOs,
            rewards: rewardDTOs
        }, function(err) {
            if (!err) {
                logger.info('SNG Tournament result notification sent:', {
                    tournament_id: tournament.id,
                    player_count: playerDTOs.length,
                    reward_count: rewardDTOs.length
                });
            }
        });

        // Log the tournament result notification
        logger.info('SNG Tournament result notification sent:', {
            tournament_id: tournament.id,
            player_count: playerDTOs.length,
            reward_count: rewardDTOs.length
        });
    }).catch(function(err) {
        logger.error('Error getting tournament rewards:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);

        // Send notification without rewards as fallback
        self.sendPushMessage(channelService, tournament.id.toString(), {
            route: consts.GAME.ROUTER.SNG_TOURNAMENT_RESULT,
            tournament: dto,
            players: playerDTOs,
            rewards: []
        });
    });
};

/**
 * Handle player leaving tournament
 *
 * @param {number} tournamentId - Tournament ID
 * @param {number} playerId - Player ID
 * @param {function} callback - Optional callback function with (error, result)
 */
SngTournamentService.prototype.handlePlayerLeave = function(tournamentId, playerId, callback) {
    var self = this;
    callback = callback || function() {};

    // Get tournament
    models.SngTournament.findByPk(tournamentId).then(function(tournament) {
        if (!tournament) {
            return callback(new Error('Tournament not found'));
        }

        // If tournament hasn't started yet, refund player
        if (tournament.status === 'WAITING') {
            // Find player registration
            models.SngTournamentPlayer.findOne({
                where: {
                    tournament_id: tournamentId,
                    player_id: playerId
                }
            }).then(function(registration) {
                if (!registration) {
                    return callback(new Error('Player not registered for this tournament'));
                }

                // Remove registration
                registration.destroy().then(function() {
                    // Refund player (partial refund)
                    var refundAmount = Math.floor((tournament.buy_in + tournament.fee) * sngConsts.DEFAULT_SETTINGS.REFUND_PERCENTAGE / 100);

                    models.Player.findByPk(playerId).then(function(player) {
                        if (player) {
                            const currentBalance = player?.balance || 0;
                            player.balance += refundAmount;
                            player.save().then(function() {
                                // Update tournament reward pool
                                tournament.reward_pool -= tournament.buy_in;
                                tournament.save().catch(function(err) {
                                    logger.error('Error updating tournament reward pool:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
                                });

                                // Log refund
                                self.logTournamentAction(playerId, tournamentId, logBoards.SNG_REFUND, {
                                    refund_amount: refundAmount,
                                    refund_percentage: sngConsts.DEFAULT_SETTINGS.REFUND_PERCENTAGE
                                });

                                // Add log into table: transactions
                                models.Transactions.create({
                                    player_id: playerId,
                                    amount: refundAmount,
                                    before_balance: currentBalance,
                                    after_balance: player.balance,
                                    type: 'SNG_TOURNAMENT',
                                    action: logBoards.SNG_REFUND,
                                    reference_id: tournamentId,
                                    reference_type: 'SNG_TOURNAMENT',
                                    meta: {
                                        tournament_id: tournamentId,
                                        refund_amount: refundAmount,
                                        refund_percentage: sngConsts.DEFAULT_SETTINGS.REFUND_PERCENTAGE
                                    },
                                    description: 'Refund for SNG Tournament',
                                });

                                // Remove from player-tournament mapping
                                delete self.playerTournaments[playerId];

                                // Return success with refund information
                                callback(null, {
                                    refund_amount: refundAmount,
                                    player: {
                                        id: player.id,
                                        balance: player.balance
                                    }
                                });
                            }).catch(function(err) {
                                logger.error('Error updating player balance:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
                                callback(err);
                            });
                        } else {
                            callback(new Error('Player not found'));
                        }
                    }).catch(function(err) {
                        logger.error('Error finding player:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
                        callback(err);
                    });
                }).catch(function(err) {
                    logger.error('Error removing player registration:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
                    callback(err);
                });
            }).catch(function(err) {
                logger.error('Error finding player registration:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
                callback(err);
            });
        } else if (tournament.status === 'IN_PROGRESS') {
            // If tournament is in progress, treat as elimination
            // Find player in tournament table
            var tableObj = self.tournamentTables[tournamentId];
            logger.info("[sngTournamentService.handlePlayerLeave] tableObj: ", tableObj);
            if (tableObj && tableObj.table) {
                var player = _.find(tableObj.table.players, function(p) {
                    return p.id === playerId;
                });

                if (player) {
                    logger.info("[sngTournamentService.handlePlayerLeave] player: ", player);
                    // Force player to fold if it's their turn
                    if (tableObj.table.currentPlayer === player.actorNr) {
                        player.Fold();
                    }

                    // Remove player from table
                    tableObj.table.removePlayer(player.id);

                    // Handle elimination
                    self.handlePlayerElimination(tournamentId, playerId, tableObj.table.game ? tableObj.table.game.handNumber : 0);

                    // Return success
                    callback(null, {
                        message: 'Player eliminated from tournament'
                    });
                } else {
                    callback(new Error('Player not found in tournament table'));
                }
            } else {
                callback(new Error('Tournament table not found'));
            }
        } else {
            callback(new Error('Tournament is not in a valid state for leaving'));
        }
    }).catch(function(err) {
        logger.error('Error handling player leave:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
        callback(err);
    });
};

/**
 * Get tournament for player
 */
SngTournamentService.prototype.getTournamentForPlayer = function(playerId) {
    return this.playerTournaments[playerId] || null;
};

/**
 * Check if player is in a tournament
 */
SngTournamentService.prototype.isPlayerInTournament = function(playerId) {
    return !!this.playerTournaments[playerId];
};

/**
 * Get tournament table
 */
SngTournamentService.prototype.getTournamentTable = function(tournamentId) {
    return this.tournamentTables[tournamentId] || null;
};

/**
 * Handle tournament end when a winner is determined
 * @param {number} tournamentId - Tournament ID
 * @param {number} winnerId - Winner player ID
 */
SngTournamentService.prototype.handleTournamentEnd = function(tournamentId, winnerId) {
    var self = this;

    // Get tournament
    models.SngTournament.findByPk(tournamentId, {
        include: [
            {
                model: models.SngTournamentPlayer,
                as: 'players',
                include: [
                    {
                        model: models.Player,
                        as: 'player'
                    }
                ]
            }
        ]
    }).then(function(tournament) {
        if (!tournament || tournament.status === 'ENDED') {
            return;
        }

        // Find winner player
        var winnerPlayer = _.find(tournament.players, function(player) {
            return player.player_id === winnerId;
        });

        if (!winnerPlayer) {
            logger.error('Winner player not found in tournament:', winnerId);
            return;
        }

        // Update winner status
        winnerPlayer.status = 'WINNER';
        winnerPlayer.rank = 1;

        winnerPlayer.save().then(function() {
            // End tournament
            self.endTournament(tournamentId);
        }).catch(function(err) {
            logger.error('Error updating winner status:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
        });
    }).catch(function(err) {
        logger.error('Error handling tournament end:', err, ' -> message: ', err.message, ' -> stack: ', err.stack);
    });
};
