# Tài liệu hướng dẫn sử dụng tính năng SNG Tournament

## 1. Khởi tạo và lấy danh sách giải đấu

### 1.1. L<PERSON>y danh sách giải đấu

**Hàm sử dụng:** `game.sngTournamentHandler.getTournaments`

**<PERSON><PERSON> tả:** L<PERSON>y danh sách các giải đấu SNG hiện có, bao gồm cả giải đấu đang chờ người chơi và đang diễn ra.

**Tham số:**

- Không có tham số đặc biệt

**Kết quả trả về:**

```javascript
{
  code: 200,
  tournament_types: [
    {
      type: "5_PLAYERS",
      name: "5 Players",
      levels: [
        {
          level: "BEGINNER",
          name: "Beginner",
          tournaments: [
            {
              id: 1,
              code: "SNG_20250515_001",
              status: "WAITING",
              player_capacity: 5,
              buy_in: 50000000,
              fee: 5000000,
              reward_pool: 0,
              created_at: "2025-05-15T10:00:00Z",
              registered_players: 2, // Số người chơi đã đăng ký
              blind_level: 1,
              current_small_blind: 500000,
              current_big_blind: 1000000,
              current_ante: 0
            },
            // Các giải đấu khác cùng loại và cấp độ...
          ],
          total_players: 2 // Tổng số người chơi đã đăng ký cho loại và cấp độ này
        },
        {
          level: "INTERMEDIATE",
          name: "Intermediate",
          tournaments: [
            // Các giải đấu cấp độ trung cấp...
          ],
          total_players: 0
        },
        {
          level: "ADVANCED",
          name: "Advanced",
          tournaments: [
            // Các giải đấu cấp độ cao cấp...
          ],
          total_players: 0
        },
        {
          level: "PRO",
          name: "Pro",
          tournaments: [
            // Các giải đấu cấp độ pro...
          ],
          total_players: 0
        }
      ]
    },
    {
      type: "9_PLAYERS",
      name: "9 Players",
      levels: [
        {
          level: "BEGINNER",
          name: "Beginner",
          tournaments: [
            // Các giải đấu 9 người cấp độ sơ cấp...
          ],
          total_players: 0
        },
        // Các cấp độ khác cho loại 9 người...
      ]
    }
  ]
}
```

**Cách sử dụng:**

```javascript
pomelo.request("game.sngTournamentHandler.getTournaments", {}, function (data) {
  if (data.code === 200) {
    // Hiển thị danh sách giải đấu theo loại và cấp độ
    renderTournamentsByTypeAndLevel(data.tournament_types);

    // Hoặc có thể tạo danh sách phẳng nếu cần
    var flatTournaments = [];
    data.tournament_types.forEach(function (type) {
      type.levels.forEach(function (level) {
        level.tournaments.forEach(function (tournament) {
          flatTournaments.push(tournament);
        });
      });
    });
    renderTournamentList(flatTournaments);
  } else {
    // Xử lý lỗi
    showError(data.message);
  }
});
```

### 1.2. Lấy thông tin chi tiết giải đấu

**Hàm sử dụng:** `game.sngTournamentHandler.getTournament`

**Mô tả:** Lấy thông tin chi tiết về một giải đấu cụ thể, bao gồm thông tin người chơi và mức blind.

**Tham số:**

- `tournamentId`: ID của giải đấu cần lấy thông tin

**Kết quả trả về:**

```javascript
{
  code: 200,
  tournament: {
    id: 1,
    code: "SNG_20250515_001",
    status: "WAITING",
    player_capacity: 5,
    buy_in: 50000000,
    fee: 5000000,
    reward_pool: 50000000,
    created_at: "2025-05-15T10:00:00Z",
    started_at: null,
    ended_at: null,
    current_blind_level: 1,
    current_small_blind: 500000,
    current_big_blind: 1000000,
    current_ante: 0
  },
  players: [
    {
      id: 1,
      player_id: 123,
      player_name: "Player1",
      seat_number: 0,
      initial_chips: 100000000,
      current_chips: 100000000,
      status: "ACTIVE",
      avatar: "1"
    },
    // Các người chơi khác...
  ],
  blindLevels: [
    {
      level_number: 1,
      small_blind: 500000,
      big_blind: 1000000,
      ante: 0,
      duration_seconds: 300
    },
    // Các mức blind khác...
  ]
}
```

**Cách sử dụng:**

```javascript
pomelo.request(
  "game.sngTournamentHandler.getTournament",
  {
    tournamentId: 1,
  },
  function (data) {
    if (data.code === 200) {
      // Hiển thị thông tin chi tiết giải đấu
      renderTournamentDetails(data.tournament, data.players, data.blindLevels);
    } else {
      // Xử lý lỗi
      showError(data.message);
    }
  }
);
```

### 1.3. Tạo giải đấu mới (chỉ dành cho admin)

**Hàm sử dụng:** `game.sngTournamentHandler.createTournament`

**Mô tả:** Tạo một giải đấu SNG mới.

**Tham số:**

- `type`: Loại giải đấu (5_PLAYERS hoặc 9_PLAYERS)
- `level`: Cấp độ giải đấu (BEGINNER, INTERMEDIATE, ADVANCED, PRO)
- `player_capacity`: Số lượng người chơi tối đa (5 hoặc 9)
- `buy_in`: Phí tham gia (chips)
- `fee`: Phí dịch vụ (chips)
- `name`: Tên giải đấu (tùy chọn)

**Kết quả trả về:**

```javascript
{
  code: 200,
  tournament: {
    id: 2,
    code: "SNG_20250515_002",
    status: "WAITING",
    player_capacity: 5,
    buy_in: 50000000,
    fee: 5000000,
    reward_pool: 0,
    created_at: "2025-05-15T11:00:00Z"
  }
}
```

**Cách sử dụng:**

```javascript
pomelo.request(
  "game.sngTournamentHandler.createTournament",
  {
    type: "5_PLAYERS",
    level: "BEGINNER",
    player_capacity: 5,
    buy_in: 50000000,
    fee: 5000000,
    name: "Giải đấu SNG Beginner",
  },
  function (data) {
    if (data.code === 200) {
      // Hiển thị thông báo thành công
      showSuccess("Giải đấu đã được tạo thành công!");
      // Cập nhật danh sách giải đấu
      refreshTournamentList();
    } else {
      // Xử lý lỗi
      showError(data.message);
    }
  }
);
```

## 2. Đăng ký và tham gia giải đấu

### 2.1. Đăng ký tham gia giải đấu

**Hàm sử dụng:** `game.sngTournamentHandler.registerTournament`

**Mô tả:** Đăng ký tham gia một giải đấu SNG. Hệ thống sẽ tự động tìm giải đấu phù hợp với loại và cấp độ được chỉ định.

**Tham số:**

- `type`: Loại giải đấu (5_PLAYERS hoặc 9_PLAYERS)
- `level`: Cấp độ giải đấu (BEGINNER, INTERMEDIATE, ADVANCED, PRO)

**Kết quả trả về:**

```javascript
{
  code: 200,
  route: "game.sngTournamentHandler.registerTournament",
  tournament_id: 1,
  tournament: {
    id: 1,
    code: "SNG_20250515_001",
    status: "WAITING",
    player_capacity: 5,
    buy_in: 50000000,
    fee: 5000000,
    reward_pool: 50000000,
    created_at: "2025-05-15T10:00:00Z",
    started_at: null,
    ended_at: null,
    registered_players: 1,
    blind_level: 1,
    current_small_blind: 500000,
    current_big_blind: 1000000,
    current_ante: 0
  },
  players: [
    {
      id: 1,
      player_id: 123,
      player_name: "Player1",
      seat_number: 0,
      initial_chips: 100000000,
      current_chips: 100000000,
      status: "ACTIVE",
      joined_at: "2025-05-15T11:30:00Z",
      avatar: "1",
      level: 5,
      vippoint: 1000,
      exp: 5000
    }
  ],
  blindLevels: [
    {
      level_number: 1,
      small_blind: 500000,
      big_blind: 1000000,
      ante: 0,
      duration_seconds: 300
    },
    // Các mức blind khác...
  ]
}
```

**Cách sử dụng:**

```javascript
pomelo.request(
  "game.sngTournamentHandler.registerTournament",
  {
    type: "5_PLAYERS",
    level: "BEGINNER",
  },
  function (data) {
    if (data.code === 200) {
      // Hiển thị thông báo thành công
      showSuccess("Đăng ký giải đấu thành công!");
      // Lưu thông tin giải đấu
      currentTournament = data.tournament;
      // Cập nhật danh sách người chơi
      tournamentPlayers = data.players;
      // Cập nhật giao diện
      updateTournamentUI();
    } else {
      // Xử lý lỗi
      showError(data.error);
    }
  }
);
```

### 2.2. Rời khỏi giải đấu

**Hàm sử dụng:** `game.sngTournamentHandler.leaveTournament`

**Mô tả:** Rời khỏi một giải đấu SNG đã đăng ký (chỉ có thể rời khi giải đấu chưa bắt đầu). Hệ thống sẽ tự động kiểm tra xem người chơi hiện tại có đang đăng ký chờ giải đấu nào không và rời khỏi giải đấu đó.

**Tham số:**

- Không cần tham số, hệ thống tự động xác định giải đấu mà người chơi đã đăng ký

**Kết quả trả về:**

```javascript
{
  code: 200,
  message: "Đã rời khỏi giải đấu",
  refund_amount: 44000000, // Số tiền hoàn lại (80% phí đăng ký)
  player: {
    id: 123,
    balance: 989000000 // Số dư sau khi hoàn phí
  }
}
```

**Cách sử dụng:**

```javascript
pomelo.request(
  "game.sngTournamentHandler.leaveTournament",
  {}, // Không cần tham số
  function (data) {
    if (data.code === 200) {
      // Hiển thị thông báo thành công
      showSuccess(
        "Đã rời khỏi giải đấu! Bạn được hoàn lại " +
          formatChips(data.refund_amount) +
          " chips."
      );
      // Cập nhật số dư người chơi
      updatePlayerBalance(data.player.balance);
    } else {
      // Xử lý lỗi
      showError(data.error);
    }
  }
);
```

## 3. Tham gia chơi trong giải đấu

### 3.1. Thực hiện hành động trong giải đấu

**Hàm sử dụng:** `game.sngTournamentHandler.execute`

**Mô tả:** Thực hiện các hành động trong giải đấu SNG (tương tự như trong bàn chơi thường).

**Tham số:**

- `tournamentId`: ID của giải đấu
- `action`: Hành động cần thực hiện (fold, check, call, raise, allin)
- `amount`: Số tiền cược (chỉ cần thiết cho hành động raise)

**Kết quả trả về:**

- Không trả về kết quả trực tiếp, thay vào đó sẽ gửi các sự kiện thông qua các kênh đã đăng ký

**Cách sử dụng:**

```javascript
pomelo.request(
  "game.sngTournamentHandler.execute",
  {
    tournamentId: 1,
    action: "call",
    amount: 0, // Không cần thiết cho hành động call
  },
  function (data) {
    if (data.code === 200) {
      // Hành động đã được gửi thành công
      console.log("Hành động đã được gửi");
    } else {
      // Xử lý lỗi
      showError(data.message);
    }
  }
);
```

## 4. Đăng ký lắng nghe các sự kiện

Để nhận được các cập nhật từ giải đấu, client cần đăng ký lắng nghe các sự kiện sau:

### 4.1. Đăng ký kênh giải đấu

**Mô tả:** Đăng ký kênh để nhận các sự kiện liên quan đến giải đấu.

**Cách sử dụng:**

```javascript
// Đăng ký kênh giải đấu
pomelo.on("onSngTournamentStatus", function (data) {
  // Cập nhật trạng thái giải đấu
  updateTournamentStatus(data);
});

pomelo.on("onSngTournamentJoin", function (data) {
  // Cập nhật danh sách người chơi khi có người tham gia
  updateTournamentPlayers(data);
});

pomelo.on("onSngBlindUpdate", function (data) {
  // Cập nhật mức blind khi tăng
  updateBlindLevel(data);
});

pomelo.on("onSngPlayerEliminated", function (data) {
  // Xử lý khi có người chơi bị loại
  handlePlayerElimination(data);
});

pomelo.on("onSngTournamentResult", function (data) {
  // Hiển thị kết quả giải đấu khi kết thúc
  showTournamentResults(data);
});
```

### 4.2. Đăng ký kênh bàn chơi

**Mô tả:** Đăng ký kênh để nhận các sự kiện liên quan đến bàn chơi (tương tự như trong bàn chơi thường).

**Cách sử dụng:**

```javascript
// Đăng ký các sự kiện bàn chơi
pomelo.on("onTableEvent", function (data) {
  // Xử lý sự kiện bàn chơi
  handleTableEvent(data);
});

pomelo.on("onStartTurn", function (data) {
  // Xử lý khi bắt đầu lượt chơi mới
  handleStartTurn(data);
});

pomelo.on("onEndTurn", function (data) {
  // Xử lý khi kết thúc lượt chơi
  handleEndTurn(data);
});

pomelo.on("onEndGame", function (data) {
  // Xử lý khi kết thúc ván chơi
  handleEndGame(data);
});
```

## 5. Xử lý các sự kiện

### 5.1. Sự kiện trạng thái giải đấu (onSngTournamentStatus)

**Mô tả:** Được gửi khi trạng thái giải đấu thay đổi.

**Dữ liệu sự kiện:**

```javascript
{
  tournament: {
    id: 1,
    code: "SNG_20250515_001",
    status: "IN_PROGRESS", // WAITING, READY, IN_PROGRESS, ENDED
    player_capacity: 5,
    buy_in: 50000000,
    fee: 5000000,
    reward_pool: 250000000,
    created_at: "2025-05-15T10:00:00Z",
    started_at: "2025-05-15T12:00:00Z",
    ended_at: null,
    current_blind_level: 1,
    current_small_blind: 500000,
    current_big_blind: 1000000,
    current_ante: 0
  },
  players: [
    // Danh sách người chơi...
  ]
}
```

**Cách xử lý:**

```javascript
function updateTournamentStatus(data) {
  // Cập nhật thông tin giải đấu
  currentTournament = data.tournament;

  // Cập nhật danh sách người chơi
  tournamentPlayers = data.players;

  // Cập nhật giao diện
  updateTournamentUI();

  // Xử lý các trạng thái khác nhau
  if (data.tournament.status === "READY") {
    // Hiển thị đếm ngược
    showCountdown();
  } else if (data.tournament.status === "IN_PROGRESS") {
    // Chuyển sang giao diện bàn chơi
    showGameTable();
  } else if (data.tournament.status === "ENDED") {
    // Hiển thị kết quả giải đấu
    showTournamentResults();
  }
}
```

### 5.2. Sự kiện tăng mức blind (onSngBlindUpdate)

**Mô tả:** Được gửi khi mức blind tăng lên.

**Dữ liệu sự kiện:**

```javascript
{
  tournament_id: 1,
  level: 2,
  small_blind: 1000000,
  big_blind: 2000000,
  ante: 0,
  active_players: 4,
  total_players: 5,
  duration_seconds: 300,
  next_level_time: "2025-05-15T12:10:00Z"
}
```

**Cách xử lý:**

```javascript
function updateBlindLevel(data) {
  // Cập nhật thông tin mức blind
  currentBlindLevel = data.level;
  currentSmallBlind = data.small_blind;
  currentBigBlind = data.big_blind;
  currentAnte = data.ante;

  // Hiển thị thông báo tăng mức blind
  showBlindIncreaseNotification(data);

  // Cập nhật giao diện
  updateBlindLevelUI();

  // Bắt đầu đếm ngược cho mức blind tiếp theo
  startBlindCountdown(data.next_level_time);
}
```

### 5.3. Sự kiện người chơi bị loại (onSngPlayerEliminated)

**Mô tả:** Được gửi khi có người chơi bị loại khỏi giải đấu.

**Dữ liệu sự kiện:**

```javascript
{
  tournament_id: 1,
  player: {
    id: 1,
    player_id: 456,
    player_name: "Player2",
    seat_number: 1,
    initial_chips: 100000000,
    current_chips: 0,
    status: "ELIMINATED",
    eliminated_at_hand: 5,
    rank: 5,
    avatar: "2"
  },
  remaining_players: 4,
  total_players: 5,
  current_blind_level: 2,
  small_blind: 1000000,
  big_blind: 2000000,
  ante: 0
}
```

**Cách xử lý:**

```javascript
function handlePlayerElimination(data) {
  // Cập nhật danh sách người chơi
  updatePlayerStatus(data.player);

  // Hiển thị thông báo người chơi bị loại
  showPlayerEliminationNotification(data.player);

  // Cập nhật số người chơi còn lại
  updateRemainingPlayersUI(data.remaining_players, data.total_players);

  // Nếu người chơi bị loại là người dùng hiện tại
  if (data.player.player_id === currentPlayerId) {
    // Hiển thị thông báo bị loại
    showEliminationMessage(data.player.rank);
  }
}
```

### 5.4. Sự kiện kết quả giải đấu (onSngTournamentResult)

**Mô tả:** Được gửi khi giải đấu kết thúc.

**Dữ liệu sự kiện:**

```javascript
{
  tournament: {
    id: 1,
    code: "SNG_20250515_001",
    status: "ENDED",
    player_capacity: 5,
    buy_in: 50000000,
    fee: 5000000,
    reward_pool: 250000000,
    created_at: "2025-05-15T10:00:00Z",
    started_at: "2025-05-15T12:00:00Z",
    ended_at: "2025-05-15T13:00:00Z"
  },
  players: [
    {
      id: 1,
      player_id: 123,
      player_name: "Player1",
      seat_number: 0,
      initial_chips: 100000000,
      current_chips: 500000000,
      status: "WINNER",
      rank: 1,
      avatar: "1",
      level: 5,
      vippoint: 1000,
      exp: 5000
    },
    {
      id: 2,
      player_id: 456,
      player_name: "Player2",
      seat_number: 1,
      initial_chips: 100000000,
      current_chips: 0,
      status: "ELIMINATED",
      eliminated_at_hand: 15,
      rank: 2,
      avatar: "2",
      level: 3,
      vippoint: 500,
      exp: 2500
    },
    // Các người chơi khác...
  ],
  rewards: [
    {
      id: 1,
      tournament_id: 1,
      player_id: 123,
      rank: 1,
      reward_amount: 125000000,
      rewarded_at: "2025-05-15T13:00:00Z",
      player_name: "Player1",
      avatar: "1"
    },
    {
      id: 2,
      tournament_id: 1,
      player_id: 456,
      rank: 2,
      reward_amount: 75000000,
      rewarded_at: "2025-05-15T13:00:00Z",
      player_name: "Player2",
      avatar: "2"
    },
    {
      id: 3,
      tournament_id: 1,
      player_id: 789,
      rank: 3,
      reward_amount: 50000000,
      rewarded_at: "2025-05-15T13:00:00Z",
      player_name: "Player3",
      avatar: "3"
    }
  ]
}
```

**Cách xử lý:**

```javascript
function showTournamentResults(data) {
  // Cập nhật thông tin giải đấu
  currentTournament = data.tournament;

  // Cập nhật danh sách người chơi
  tournamentPlayers = data.players;

  // Cập nhật thông tin giải thưởng
  tournamentRewards = data.rewards;

  // Hiển thị bảng kết quả giải đấu
  showResultsTable(data.players, data.rewards);

  // Nếu người dùng hiện tại có trong danh sách giải thưởng
  var currentPlayerReward = data.rewards.find(
    (reward) => reward.player_id === currentPlayerId
  );
  if (currentPlayerReward) {
    // Hiển thị thông báo nhận thưởng
    showRewardNotification(currentPlayerReward);

    // Cập nhật số dư người chơi
    updatePlayerBalance();
  }

  // Hiển thị nút quay lại danh sách giải đấu
  showReturnToLobbyButton();
}
```

## 6. Luồng hoạt động tổng thể

### 6.1. Luồng đăng ký và tham gia giải đấu

1. Client lấy danh sách giải đấu bằng cách gọi `game.sngTournamentHandler.getTournaments`
2. Người chơi chọn loại giải đấu (5_PLAYERS hoặc 9_PLAYERS) và cấp độ (BEGINNER, INTERMEDIATE, ADVANCED, PRO)
3. Người chơi đăng ký tham gia giải đấu bằng cách gọi `game.sngTournamentHandler.registerTournament` với tham số `type` và `level`
4. Hệ thống tự động tìm giải đấu phù hợp với loại và cấp độ được chỉ định, hoặc tạo mới nếu cần
5. Client nhận thông tin chi tiết về giải đấu đã đăng ký trong kết quả trả về
6. Client nhận sự kiện `onSngTournamentJoin` khi có người chơi mới tham gia
7. Khi đủ số lượng người chơi, giải đấu chuyển sang trạng thái READY và client nhận sự kiện `onSngTournamentStatus`
8. Hệ thống tự động tạo một giải đấu mới với cùng cấu hình để thay thế giải đấu đã đủ người
9. Sau khi đếm ngược 5 giây, giải đấu bắt đầu và chuyển sang trạng thái IN_PROGRESS

### 6.2. Luồng chơi trong giải đấu

1. Client nhận sự kiện `onTableEvent` với thông tin bàn chơi
2. Client nhận sự kiện `onStartTurn` khi bắt đầu vòng chơi mới
3. Người chơi thực hiện các hành động bằng cách gọi `game.sngTournamentHandler.execute`
4. Client nhận sự kiện `onEndTurn` khi kết thúc vòng chơi
5. Mỗi 5 phút, client nhận sự kiện `onSngBlindUpdate` khi mức blind tăng
6. Khi người chơi hết chips, client nhận sự kiện `onSngPlayerEliminated`
7. Khi chỉ còn một người chơi, giải đấu kết thúc và client nhận sự kiện `onSngTournamentResult`

### 6.3. Luồng kết thúc giải đấu

1. Giải đấu chuyển sang trạng thái ENDED
2. Client nhận sự kiện `onSngTournamentResult` với thông tin kết quả giải đấu
3. Người chơi xem kết quả và nhận thưởng (nếu có)
4. Người chơi quay lại danh sách giải đấu để tham gia giải đấu khác

## 7. Cấu hình giải đấu

Cấu hình giải đấu được định nghĩa trong file `game-server/config/data/sngTournaments.json` với các thông số sau:

```javascript
{
  "tournaments": [
    {
      "id": "sng_5_beginner",
      "name": "SNG 5 Players - Beginner",
      "type": "5_PLAYERS",
      "level": "BEGINNER",
      "player_capacity": 5,
      "buy_in": 50000000,
      "fee": 5000000,
      "initial_chips": 100000000,
      "description": "5-player Sit and Go tournament for beginners"
    },
    // Các loại giải đấu khác...
  ],
  "blind_structure": [
    { "level": 1, "smallBlind": 500000, "bigBlind": 1000000, "ante": 0, "duration_seconds": 300 },
    // Các mức blind khác...
  ],
  "reward_distribution": {
    "first_place": 50,
    "second_place": 30,
    "third_place": 20
  },
  "settings": {
    "countdown_seconds": 5,
    "blind_increase_minutes": 5,
    "refund_percentage": 80
  }
}
```

## 8. Tự động tạo giải đấu mới

Hệ thống tự động tạo giải đấu mới để đảm bảo luôn có giải đấu sẵn sàng cho người chơi tham gia.

### 8.1. Cấu hình giải đấu

Hệ thống sử dụng các cấu hình sau để quản lý việc tạo giải đấu:

- `MAX_TOURNAMENTS_PER_TYPE_LEVEL`: Số lượng tối đa giải đấu cho mỗi loại và cấp độ (mặc định: 1)
- `INITIAL_TOURNAMENTS_PER_TYPE_LEVEL`: Số lượng giải đấu ban đầu cho mỗi loại và cấp độ khi khởi động hệ thống (mặc định: 1)

Các cấu hình này có thể được điều chỉnh trong file `sngConsts.js`.

### 8.2. Khởi tạo ban đầu

Khi hệ thống khởi động, nó sẽ tự động kiểm tra và tạo giải đấu cho mỗi loại và cấp độ nếu chưa có:

- 5 người chơi: Beginner, Intermediate, Advanced, Pro
- 9 người chơi: Beginner, Intermediate, Advanced, Pro

Hệ thống sẽ kiểm tra số lượng giải đấu hiện có trong cơ sở dữ liệu:

1. Nếu không có giải đấu nào, hệ thống sẽ tạo chính xác một giải đấu cho mỗi loại và cấp độ (tổng cộng 8 giải đấu)
2. Nếu đã có giải đấu, hệ thống sẽ kiểm tra từng loại và cấp độ để đảm bảo có ít nhất một giải đấu ở trạng thái WAITING

Hệ thống sử dụng cờ đánh dấu `initialTournamentsCreated` để đảm bảo chỉ tạo giải đấu ban đầu một lần, ngay cả khi có nhiều lần khởi động lại hoặc kết nối lại.

### 8.3. Tạo giải đấu khi giải đấu hiện tại đã đủ người

Khi một giải đấu đạt đủ số lượng người chơi và chuyển sang trạng thái READY, hệ thống sẽ kiểm tra:

1. Số lượng tổng giải đấu của loại và cấp độ đó đã đạt tối đa chưa (`MAX_TOURNAMENTS_PER_TYPE_LEVEL`)
2. Đã có giải đấu nào ở trạng thái WAITING cho loại và cấp độ đó chưa

Nếu chưa đạt tối đa và chưa có giải đấu nào ở trạng thái WAITING, hệ thống sẽ tạo một giải đấu mới với cùng cấu hình:

- Cùng số lượng người chơi (5 hoặc 9)
- Cùng mức buy-in và phí
- Cùng cấp độ (Beginner, Intermediate, Advanced, Pro)

### 8.4. Cách hoạt động

Quá trình tự động tạo giải đấu mới diễn ra như sau:

1. Khi một giải đấu đạt đủ số lượng người chơi, hệ thống gọi hàm `createSimilarTournament` để kiểm tra và tạo một giải đấu mới nếu cần.
2. Hệ thống xác định loại và cấp độ của giải đấu dựa trên số lượng người chơi và mức buy-in.
3. Hệ thống kiểm tra số lượng giải đấu hiện có và số lượng giải đấu ở trạng thái WAITING.
4. Nếu cần thiết, hệ thống tạo một giải đấu mới với cùng cấu hình và đặt nó ở trạng thái WAITING.
5. Giải đấu mới sẽ xuất hiện trong danh sách giải đấu và sẵn sàng cho người chơi đăng ký.

### 8.5. Cải tiến để tránh tạo quá nhiều giải đấu

Để tránh tạo quá nhiều giải đấu khi khởi động lại server với nhiều worker, hệ thống đã được cải tiến với các cơ chế sau:

1. **Cờ đánh dấu khởi tạo**: Sử dụng biến `initialTournamentsCreated` để đảm bảo mỗi worker chỉ tạo giải đấu ban đầu một lần.

2. **Khóa cơ sở dữ liệu**: Sử dụng khóa tư vấn MySQL (MySQL advisory lock) `GET_LOCK()` để đảm bảo chỉ một worker có thể tạo giải đấu tại một thời điểm.

3. **Kiểm tra số lượng hiện có**: Trước khi tạo giải đấu mới, hệ thống kiểm tra tổng số giải đấu hiện có trong cơ sở dữ liệu.

4. **Xử lý tuần tự**: Sử dụng chuỗi Promise để xử lý tuần tự việc tạo giải đấu, tránh tạo đồng thời nhiều giải đấu.

5. **Kiểm tra trạng thái WAITING**: Chỉ tạo giải đấu mới nếu không có giải đấu nào ở trạng thái WAITING cho loại và cấp độ đó.

6. **Giới hạn số lượng tối đa**: Sử dụng cấu hình `MAX_TOURNAMENTS_PER_TYPE_LEVEL` để giới hạn số lượng tối đa giải đấu cho mỗi loại và cấp độ.

7. **Độ trễ ngẫu nhiên**: Thêm độ trễ ngẫu nhiên trước khi kiểm tra và tạo giải đấu để tránh nhiều worker cùng cố gắng tạo giải đấu đồng thời.

8. **Ghi log chi tiết**: Thêm thông tin worker ID vào các thông báo log để dễ dàng theo dõi và gỡ lỗi.

9. **Xử lý lỗi tốt hơn**: Cải thiện xử lý lỗi để tránh crash khi có lỗi xảy ra trong quá trình tạo giải đấu.

Cơ chế này đảm bảo không tạo quá nhiều giải đấu không cần thiết, đồng thời vẫn đảm bảo luôn có ít nhất một giải đấu ở trạng thái WAITING cho mỗi loại và cấp độ, ngay cả khi server chạy với nhiều worker.

## 9. Hệ thống ghi log giải đấu

### 9.1. Bảng sng_tournament_logs

Hệ thống sử dụng bảng `sng_tournament_logs` để lưu trữ chi tiết các hành động và sự kiện trong giải đấu SNG. Bảng này giúp theo dõi toàn bộ quá trình diễn ra của giải đấu, từ đăng ký, tăng mức blind, loại bỏ người chơi đến phân phối giải thưởng.

**Cấu trúc bảng:**

- `id`: ID bản ghi
- `tournament_id`: ID giải đấu
- `player_id`: ID người chơi (có thể null nếu là hành động hệ thống)
- `action_type`: Loại hành động
- `data`: Dữ liệu chi tiết về hành động (JSON)
- `amount`: Số tiền liên quan đến hành động (nếu có)
- `created_at`: Thời điểm ghi log

**Các loại hành động được ghi log:**

1. **SNG_REGISTER**: Đăng ký tham gia giải đấu

   - Dữ liệu: buy_in, fee, initial_chips, tournament_id
   - Người chơi: ID người đăng ký

2. **SNG_REFUND**: Hoàn phí khi rời khỏi giải đấu

   - Dữ liệu: refund_amount, refund_percentage, tournament_id
   - Người chơi: ID người rời giải đấu

3. **SNG_BLIND_INCREASE**: Tăng mức blind

   - Dữ liệu: level, small_blind, big_blind, ante, active_players, tournament_id
   - Người chơi: null (hành động hệ thống)

4. **SNG_PLAYER_ELIMINATED**: Người chơi bị loại

   - Dữ liệu: hand_number, rank, active_players_remaining, tournament_id
   - Người chơi: ID người bị loại

5. **SNG_REWARD**: Nhận thưởng giải đấu

   - Dữ liệu: rank, reward_amount, percentage, tournament_id
   - Người chơi: ID người nhận thưởng

6. **SNG_TOURNAMENT_END**: Kết thúc giải đấu
   - Dữ liệu: reward_pool, player_count, duration_minutes, started_at, ended_at, player_stats, final_blind_level, tournament_id
   - Người chơi: null (hành động hệ thống)

### 9.2. Cách sử dụng logs

Các logs này có thể được sử dụng để:

1. **Phân tích hiệu suất giải đấu**: Theo dõi thời gian trung bình của giải đấu, số người chơi, tỷ lệ hoàn thành, v.v.
2. **Giải quyết tranh chấp**: Kiểm tra lịch sử các hành động để giải quyết khiếu nại của người chơi
3. **Phát hiện gian lận**: Phân tích mẫu hành vi bất thường
4. **Báo cáo thống kê**: Tạo báo cáo về số lượng giải đấu, số người tham gia, tổng giải thưởng, v.v.
5. **Theo dõi tiến trình**: Xem chi tiết tiến trình của một giải đấu cụ thể

### 9.3. Truy vấn logs

Ví dụ truy vấn để lấy tất cả logs của một giải đấu:

```sql
SELECT * FROM sng_tournament_logs
WHERE tournament_id = 123
ORDER BY created_at ASC;
```

Ví dụ truy vấn để lấy tất cả logs của một người chơi:

```sql
SELECT * FROM sng_tournament_logs
WHERE player_id = 456
ORDER BY created_at DESC;
```

## 10. Push Message Format và Helper Functions

### 10.1. Helper Functions

Hệ thống SNG Tournament sử dụng các helper functions để tối ưu hóa việc gửi thông báo:

#### getChannelReceivers(channel)

- Lấy danh sách receivers từ channel với format `{uid, sid}`
- Trả về array các receivers để sử dụng với `pushMessageByUids`

#### sendPushMessage(channelService, channelName, message, callback)

- Gửi push message với format đúng sử dụng `channelService.pushMessageByUids()`
- Xử lý lỗi và logging tự động
- Đảm bảo message được gửi với format: `groups: {"connector-server-3":[561]}`

### 10.2. Push Message Events

Tất cả SNG Tournament events được gửi với format đúng:

1. **SNG_TOURNAMENT_JOIN**: Thông báo khi có người chơi mới tham gia
2. **SNG_TOURNAMENT_STATUS**: Cập nhật trạng thái giải đấu
3. **SNG_TOURNAMENT_BLIND_UPDATE**: Thông báo tăng mức blind
4. **SNG_TOURNAMENT_PLAYER_ELIMINATED**: Thông báo người chơi bị loại
5. **SNG_TOURNAMENT_RESULT**: Kết quả cuối cùng của giải đấu

## 11. Lưu ý quan trọng

1. Giải đấu SNG sử dụng cùng logic chơi bài với bàn chơi thường, nhưng có thêm các tính năng đặc biệt như tăng mức blind, loại bỏ người chơi và phân phối giải thưởng.
2. Người chơi không thể rời khỏi giải đấu sau khi giải đấu đã bắt đầu.
3. Không có tính năng mua lại (rebuy) trong giải đấu SNG.
4. Mức blind tăng theo thời gian, không phải theo số vòng chơi.
5. Giải thưởng được phân phối dựa trên thứ hạng cuối cùng của người chơi.
6. Hệ thống luôn đảm bảo có ít nhất một giải đấu ở trạng thái WAITING cho mỗi loại và cấp độ.
7. Tất cả các hành động quan trọng trong giải đấu đều được ghi log vào bảng `sng_tournament_logs` để theo dõi và phân tích.
8. Push messages sử dụng `channelService.pushMessageByUids()` để đảm bảo format đúng và tương thích với hệ thống bàn chơi thường.

## 12. Logic tiền ảo và vị trí ngồi

### 12.1. Vị trí ngồi (seat_number)

- **Khi đăng ký**: `seat_number` = `null` (chưa có vị trí cụ thể)
- **Khi tournament bắt đầu**: Hệ thống tự động gán vị trí ngồi tuần tự (0, 1, 2, ...)
- **Lý do**: Vị trí ngồi chỉ có ý nghĩa khi bàn chơi được tạo và game bắt đầu

### 12.2. Tiền ảo trong tournament

- **Tiền thật**: Người chơi trả `buy_in + fee` từ `player.balance` (tiền thật)
- **Tiền ảo**: Mỗi người chơi nhận 100 triệu chips ảo (`initial_chips`) để chơi trong tournament
- **Tách biệt hoàn toàn**: Tiền ảo trong tournament không ảnh hưởng đến số dư tiền thật của người chơi
- **Chỉ giải thưởng**: Chỉ có giải thưởng cuối cùng mới được cộng vào tiền thật của người chơi

### 12.3. Luồng tiền chi tiết

1. **Đăng ký tournament**:

   - Trừ tiền thật: `player.balance -= (buy_in + fee)`
   - Cấp tiền ảo: `current_chips = 100,000,000` (không liên quan tiền thật)

2. **Trong tournament**:

   - Chơi với tiền ảo: `current_chips` tăng/giảm theo kết quả game
   - Tiền thật không thay đổi: `player.balance` giữ nguyên

3. **Kết thúc tournament**:
   - Tính giải thưởng từ `reward_pool` (tổng buy_in của tất cả người chơi)
   - Cộng giải thưởng vào tiền thật: `player.balance += reward_amount`
